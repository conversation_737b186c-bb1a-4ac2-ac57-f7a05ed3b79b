# Environment Variables Configuration Guide

This guide provides the complete environment variables setup for both Admin and Staff servers.

## 🏗️ Overview

- **Staff Server**: `inno-crm-staff.vercel.app` (Current repository)
- **Admin Server**: `inno-crm-admin.vercel.app` (Separate repository)

## 📋 Required Environment Variables

### Core Variables (Both Servers)

#### Database Configuration
```env
DATABASE_URL="your-database-connection-string"
PRISMA_GENERATE_DATAPROXY="true"
```

#### Authentication
```env
NEXTAUTH_SECRET="your-super-secret-key-32-characters-minimum"
NEXTAUTH_URL="https://your-vercel-domain.vercel.app"
```

#### Application Settings
```env
APP_NAME="Innovative Centre"
APP_URL="https://your-vercel-domain.vercel.app"
APP_ENV="production"
```

## 🏢 Staff Server Environment Variables

Copy these to your **Staff Server** Vercel project:

```env
# Database Configuration - Staff Database
DATABASE_URL="postgresql://crm-staff_owner:<EMAIL>/crm-staff?sslmode=require&channel_binding=require"

# NextAuth.js Configuration
NEXTAUTH_SECRET="inno-crm-staff-super-secret-key-for-production-2024-very-long-and-secure"
NEXTAUTH_URL="https://inno-crm-staff.vercel.app"

# Prisma Configuration
PRISMA_GENERATE_DATAPROXY="true"

# Application Configuration
APP_NAME="Innovative Centre - Staff Portal"
APP_URL="https://inno-crm-staff.vercel.app"
APP_ENV="production"
SERVER_TYPE="staff"

# Inter-Server Communication
ADMIN_SERVER_URL="https://inno-crm-admin.vercel.app"
STAFF_SERVER_URL="https://inno-crm-staff.vercel.app"
INTER_SERVER_SECRET="inno-crm-inter-server-communication-secret-2024"

# SMS Service Configuration
SMS_PROVIDER="eskiz"
SMS_API_KEY="your-production-sms-api-key"
ESKIZ_API_URL="https://notify.eskiz.uz"
ESKIZ_FROM="4546"

# Email Service Configuration
EMAIL_PROVIDER="gmail"
EMAIL_USER="<EMAIL>"
EMAIL_PASSWORD="your-production-app-password"
EMAIL_FROM="Innovative Centre <<EMAIL>>"
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_SECURE="false"

# Security Configuration
BCRYPT_ROUNDS="12"
JWT_EXPIRES_IN="7d"
SESSION_MAX_AGE="604800"

# Feature Flags - Staff Server (Limited Features)
FEATURE_SMS_ENABLED="true"
FEATURE_EMAIL_ENABLED="true"
FEATURE_WORKFLOWS_ENABLED="true"
FEATURE_ANALYTICS_ENABLED="false"
FEATURE_REPORTS_ENABLED="false"
FEATURE_BULK_OPERATIONS="true"
FEATURE_ADMIN_PANEL="false"
FEATURE_FINANCIAL_REPORTS="false"
FEATURE_KPI_TRACKING="false"

# Uzbekistan Configuration
TIMEZONE="Asia/Tashkent"
CURRENCY="UZS"
LOCALE="uz-UZ"
PHONE_COUNTRY_CODE="+998"

# Business Configuration
DEFAULT_BRANCH="Main"
BRANCHES="Main,Chilonzor,Yunusobod"
DEFAULT_COURSE_DURATION="3"
DEFAULT_CLASS_DURATION="90"
MAX_STUDENTS_PER_GROUP="15"
PAYMENT_GRACE_PERIOD="7"
LATE_PAYMENT_FEE="50000"
PAYMENT_REMINDER_DAYS="3,7,14"

# Performance Configuration
PERFORMANCE_MONITORING="true"
SLOW_QUERY_THRESHOLD="1000"
MEMORY_USAGE_ALERT_THRESHOLD="80"
LOG_LEVEL="info"
LOG_FILE_ENABLED="false"
CACHE_TTL="300"
CACHE_MAX_SIZE="1000"

# Rate Limiting
RATE_LIMIT_ENABLED="true"
RATE_LIMIT_MAX_REQUESTS="100"
RATE_LIMIT_WINDOW_MS="900000"

# Notification Configuration
NOTIFICATION_BATCH_SIZE="50"
NOTIFICATION_RETRY_ATTEMPTS="3"
NOTIFICATION_RETRY_DELAY="5000"

# Workflow Configuration
WORKFLOW_ENABLED="true"
WORKFLOW_CHECK_INTERVAL="300000"
WORKFLOW_MAX_RETRIES="3"

# Monitoring Configuration
HEALTH_CHECK_ENABLED="true"
HEALTH_CHECK_INTERVAL="60000"
UPTIME_MONITORING="true"

# Production Settings
BACKUP_ENABLED="false"
AUTO_BACKUP_ENABLED="false"
DEBUG="false"
VERBOSE_LOGGING="false"
MOCK_SMS="false"
MOCK_EMAIL="false"
```

## 🔐 Admin Server Environment Variables

Copy these to your **Admin Server** Vercel project:

```env
# Database Configuration - Admin Database (Create separate database)
DATABASE_URL="************************************************************************************************************************"

# NextAuth.js Configuration
NEXTAUTH_SECRET="inno-crm-admin-super-secret-key-for-production-2024-very-long-and-secure"
NEXTAUTH_URL="https://inno-crm-admin.vercel.app"

# Prisma Configuration
PRISMA_GENERATE_DATAPROXY="true"

# Application Configuration
APP_NAME="Innovative Centre - Admin Portal"
APP_URL="https://inno-crm-admin.vercel.app"
APP_ENV="production"
SERVER_TYPE="admin"

# Inter-Server Communication
ADMIN_SERVER_URL="https://inno-crm-admin.vercel.app"
STAFF_SERVER_URL="https://inno-crm-staff.vercel.app"
INTER_SERVER_SECRET="inno-crm-inter-server-communication-secret-2024"

# SMS Service Configuration (Same as staff)
SMS_PROVIDER="eskiz"
SMS_API_KEY="your-production-sms-api-key"
ESKIZ_API_URL="https://notify.eskiz.uz"
ESKIZ_FROM="4546"

# Email Service Configuration (Same as staff)
EMAIL_PROVIDER="gmail"
EMAIL_USER="<EMAIL>"
EMAIL_PASSWORD="your-production-app-password"
EMAIL_FROM="Innovative Centre <<EMAIL>>"
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_SECURE="false"

# Security Configuration
BCRYPT_ROUNDS="12"
JWT_EXPIRES_IN="7d"
SESSION_MAX_AGE="604800"

# Feature Flags - Admin Server (All Features Enabled)
FEATURE_SMS_ENABLED="true"
FEATURE_EMAIL_ENABLED="true"
FEATURE_WORKFLOWS_ENABLED="true"
FEATURE_ANALYTICS_ENABLED="true"
FEATURE_REPORTS_ENABLED="true"
FEATURE_BULK_OPERATIONS="true"
FEATURE_ADMIN_PANEL="true"
FEATURE_FINANCIAL_REPORTS="true"
FEATURE_KPI_TRACKING="true"

# Admin-specific Security
ADMIN_IP_WHITELIST="your.admin.ip.address"
ADMIN_ACCESS_RESTRICTION="true"

# Same business and performance configuration as staff server...
# (Copy all other variables from staff server)
```

## 🔧 Setup Instructions

### For Staff Server (Current Repository)
1. Go to [Vercel Dashboard](https://vercel.com/dashboard)
2. Select your staff server project
3. Go to Settings → Environment Variables
4. Add all staff server variables above
5. Redeploy the application

### For Admin Server (New Repository)
1. Create admin server repository (see ADMIN_SERVER_SETUP.md)
2. Deploy to Vercel as new project
3. Add all admin server variables above
4. Ensure DATABASE_URL points to admin database

## 🔍 Variable Validation

### Required Variables Checklist
- [ ] DATABASE_URL (different for each server)
- [ ] NEXTAUTH_SECRET (different for each server)
- [ ] NEXTAUTH_URL (matches Vercel domain)
- [ ] PRISMA_GENERATE_DATAPROXY="true"
- [ ] SERVER_TYPE ("staff" or "admin")
- [ ] INTER_SERVER_SECRET (same for both)

### Optional but Recommended
- [ ] SMS_API_KEY (for notifications)
- [ ] EMAIL_USER and EMAIL_PASSWORD (for emails)
- [ ] ADMIN_IP_WHITELIST (for admin security)

## 🧪 Testing Environment Variables

### Test Commands
```bash
# Test database connection
npm run db:generate

# Test build process
npm run build

# Test deployment preparation
npm run prepare-deployment
```

### Verification URLs
- Staff Health: `https://inno-crm-staff.vercel.app/api/health`
- Admin Health: `https://inno-crm-admin.vercel.app/api/health`

## 🚨 Security Notes

### Secrets Management
- Use different NEXTAUTH_SECRET for each server
- Keep INTER_SERVER_SECRET secure and identical
- Rotate secrets regularly
- Never commit secrets to repository

### Database Security
- Use separate databases for admin and staff
- Implement proper access controls
- Monitor database connections
- Regular security audits

## 🔄 Updates and Maintenance

### When to Update Variables
- Domain changes
- Database migrations
- Security updates
- Feature flag changes
- Service provider changes

### Update Process
1. Update in Vercel dashboard
2. Redeploy application
3. Test functionality
4. Monitor for issues
5. Update documentation

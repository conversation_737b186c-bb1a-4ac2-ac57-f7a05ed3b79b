# Admin Server Setup Guide

This guide helps you create and configure the admin server repository for deployment.

## 🏗️ Overview

The admin server will be a separate repository based on the current staff server code, but configured for admin and cashier roles with full system access.

## 📋 Prerequisites

1. Current staff server repository (this one)
2. GitHub account
3. Admin database (separate from staff database)

## 🚀 Setup Steps

### Step 1: Create Admin Server Repository

```bash
# Clone the current repository to create admin version
git clone https://github.com/MrFarrukhT/inno-crm.git inno-crm-admin
cd inno-crm-admin

# Remove existing git history and create new repository
rm -rf .git
git init
git remote add origin https://github.com/MrFarrukhT/inno-crm-admin.git
```

### Step 2: Modify Configuration for Admin Server

1. **Update package.json:**
   ```json
   {
     "name": "inno-crm-admin",
     "scripts": {
       "dev": "next dev",
       "build": "prisma generate && next build",
       ...
     }
   }
   ```

2. **Copy admin configuration files:**
   - Copy `vercel.admin.json` to `vercel.json`
   - Copy `.env.production.admin` to `.env.production`

3. **Update README.md:**
   - Change title to "Innovative Centre CRM System - Admin Server"
   - Update description to mention admin/cashier functionality

### Step 3: Database Setup for Admin Server

You'll need to create a separate admin database. Options:

#### Option A: Create New Neon Database
1. Go to [Neon Console](https://console.neon.tech/)
2. Create new project: `inno-crm-admin`
3. Get connection string
4. Update DATABASE_URL in `.env.production`

#### Option B: Use Existing Database with Different Schema
1. Create new schema in existing database
2. Update connection string to use admin schema
3. Apply migrations to admin database

### Step 4: Environment Variables for Admin Server

Update `.env.production` with admin-specific values:

```env
# Database Configuration - Admin Database
DATABASE_URL="your-admin-database-url"

# NextAuth.js Configuration
NEXTAUTH_SECRET="inno-crm-admin-super-secret-key-for-production-2024"
NEXTAUTH_URL="https://inno-crm-admin.vercel.app"

# Application Configuration
APP_NAME="Innovative Centre - Admin Portal"
APP_URL="https://inno-crm-admin.vercel.app"
SERVER_TYPE="admin"

# Inter-Server Communication
ADMIN_SERVER_URL="https://inno-crm-admin.vercel.app"
STAFF_SERVER_URL="https://inno-crm-staff.vercel.app"
INTER_SERVER_SECRET="inno-crm-inter-server-communication-secret-2024"

# Admin-specific features
FEATURE_ADMIN_PANEL="true"
FEATURE_FINANCIAL_REPORTS="true"
FEATURE_KPI_TRACKING="true"
ADMIN_IP_WHITELIST="your.admin.ip.address"
```

### Step 5: Code Modifications for Admin Server

#### Enable Admin Features
The admin server should have access to all features. Ensure these are enabled:

1. **Analytics Dashboard** (`/dashboard/analytics`)
2. **Payment Management** (`/dashboard/payments`)
3. **Admin Panel** (`/dashboard/admin`)
4. **Financial Reports**
5. **KPI Tracking**

#### Middleware Updates
Update middleware to allow ADMIN and CASHIER roles:

```typescript
// middleware.ts
const adminRoles = ['ADMIN', 'CASHIER'];
const staffRoles = ['RECEPTION', 'ACADEMIC_MANAGER', 'TEACHER', 'MANAGER'];

// Allow admin roles for admin server
if (process.env.SERVER_TYPE === 'admin') {
  allowedRoles = [...adminRoles, ...staffRoles];
} else {
  allowedRoles = staffRoles;
}
```

### Step 6: Deploy Admin Server

1. **Push to GitHub:**
   ```bash
   git add .
   git commit -m "Initial admin server setup"
   git push origin main
   ```

2. **Deploy to Vercel:**
   - Go to [vercel.com/new](https://vercel.com/new)
   - Import the admin repository
   - Project name: `inno-crm-admin`
   - Set environment variables from `.env.production`

### Step 7: Database Migration

```bash
# In admin server directory
npm install
npx prisma generate
npx prisma db push
npx prisma db seed
```

## 🔧 Key Differences: Admin vs Staff Server

### Admin Server Features (All Enabled)
- ✅ Full analytics dashboard
- ✅ Payment management
- ✅ Financial reports
- ✅ KPI tracking
- ✅ Admin user management
- ✅ System administration
- ✅ All staff server features

### Staff Server Features (Limited)
- ✅ Lead management
- ✅ Student management
- ✅ Course/group management
- ✅ Attendance tracking
- ✅ Assessment management
- ❌ Financial analytics
- ❌ Payment management
- ❌ Admin functions

## 🔐 Security Considerations

### Admin Server Security
- IP whitelist for admin access
- Enhanced authentication
- Separate database
- Audit logging
- Role-based restrictions

### Inter-Server Communication
- Shared secret authentication
- CORS configuration
- API rate limiting
- Request validation

## 🧪 Testing

### Admin Server Tests
1. Deploy admin server
2. Test admin/cashier login
3. Verify all features accessible
4. Test financial reports
5. Check analytics dashboard
6. Verify payment management

### Integration Tests
1. Test API communication between servers
2. Verify data synchronization
3. Check role-based access
4. Test CORS configuration

## 📊 Monitoring

### Health Checks
- Admin: `https://inno-crm-admin.vercel.app/api/health`
- Staff: `https://inno-crm-staff.vercel.app/api/health`

### Metrics to Monitor
- Response times
- Database connections
- Authentication success rates
- Inter-server API calls
- Error rates

## 🚨 Troubleshooting

### Common Issues
1. **Database Connection**: Verify admin DATABASE_URL
2. **Role Access**: Check middleware configuration
3. **CORS Errors**: Verify domain settings
4. **Build Failures**: Ensure all dependencies installed

### Support Checklist
- [ ] Admin database created and accessible
- [ ] Environment variables configured
- [ ] Repository pushed to GitHub
- [ ] Vercel deployment successful
- [ ] Admin roles can access all features
- [ ] Inter-server communication working

## 📝 Next Steps

After admin server setup:
1. Configure environment variables for both deployments
2. Set up inter-server communication
3. Deploy both applications to Vercel
4. Test and verify deployments

## 🔄 Maintenance

### Regular Tasks
- Monitor both servers
- Update dependencies
- Database backups
- Security audits
- Performance optimization

### Updates
- Coordinate updates between servers
- Test compatibility
- Monitor deployment status
- Verify functionality

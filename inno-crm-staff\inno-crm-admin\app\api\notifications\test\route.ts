import { NextRequest, NextResponse } from 'next/server'

// Test endpoint for notifications that doesn't require database
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    if (action === 'mock') {
      // Return mock notifications for testing
      const mockNotifications = [
        {
          id: '1',
          title: 'New Student Enrollment',
          message: '<PERSON> has enrolled in IELTS course',
          type: 'success',
          priority: 'medium',
          read: false,
          createdAt: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
          actionUrl: '/dashboard/students'
        },
        {
          id: '2',
          title: 'Payment Received',
          message: 'Payment of $500 received from <PERSON>',
          type: 'success',
          priority: 'medium',
          read: false,
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
          actionUrl: '/dashboard/payments'
        },
        {
          id: '3',
          title: 'Class Reminder',
          message: 'B2 class starts in 30 minutes',
          type: 'info',
          priority: 'high',
          read: true,
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString(), // 4 hours ago
          actionUrl: '/dashboard/classes'
        },
        {
          id: '4',
          title: 'Low Attendance Alert',
          message: 'Student <PERSON> has missed 3 consecutive classes',
          type: 'warning',
          priority: 'high',
          read: false,
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString(), // 6 hours ago
          actionUrl: '/dashboard/attendance'
        },
        {
          id: '5',
          title: 'System Maintenance',
          message: 'Scheduled maintenance tonight at 2 AM',
          type: 'info',
          priority: 'low',
          read: true,
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
        }
      ]

      return NextResponse.json({
        notifications: mockNotifications,
        unreadCount: mockNotifications.filter(n => !n.read).length,
        total: mockNotifications.length
      })
    }

    if (action === 'status') {
      // Return notification system status
      return NextResponse.json({
        status: 'operational',
        services: {
          sms: { available: true, provider: 'mock' },
          email: { available: true, provider: 'mock' },
          push: { available: false, provider: 'none' }
        },
        stats: {
          totalSent: 1247,
          sentToday: 23,
          failureRate: 0.02
        }
      })
    }

    return NextResponse.json({
      message: 'Notification test endpoint',
      availableActions: ['mock', 'status'],
      usage: {
        mock: 'GET /api/notifications/test?action=mock - Returns mock notifications',
        status: 'GET /api/notifications/test?action=status - Returns system status'
      }
    })

  } catch (error) {
    console.error('Error in notification test endpoint:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Mock notification sending
    console.log('Mock notification sent:', body)
    
    return NextResponse.json({
      success: true,
      message: 'Mock notification sent successfully',
      data: {
        id: `mock-${Date.now()}`,
        sentAt: new Date().toISOString(),
        channels: body.channels || ['mock'],
        recipient: body.recipientId || 'test-user'
      }
    })

  } catch (error) {
    console.error('Error in mock notification sending:', error)
    return NextResponse.json(
      { error: 'Failed to send mock notification' },
      { status: 500 }
    )
  }
}

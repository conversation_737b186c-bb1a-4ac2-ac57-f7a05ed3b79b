const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function migratePaymentStatus() {
  try {
    console.log('Starting payment status migration...')

    // Get current payment statuses
    const payments = await prisma.$queryRaw`
      SELECT status, COUNT(*) as count
      FROM payments
      GROUP BY status
    `

    console.log('Current payment statuses:', payments)

    // Update PENDING and OVERDUE to DEBT
    console.log('Updating PENDING payments to DEBT...')
    const pendingResult = await prisma.$executeRaw`
      UPDATE payments
      SET status = 'DEBT'
      WHERE status = 'PENDING'
    `
    console.log(`Updated ${pendingResult} PENDING payments to DEBT`)

    console.log('Updating OVERDUE payments to DEBT...')
    const overdueResult = await prisma.$executeRaw`
      UPDATE payments
      SET status = 'DEBT'
      WHERE status = 'OVERDUE'
    `
    console.log(`Updated ${overdueResult} OVERDUE payments to DEBT`)

    // Verify the migration
    const updatedPayments = await prisma.$queryRaw`
      SELECT status, COUNT(*) as count
      FROM payments
      GROUP BY status
    `

    console.log('Updated payment statuses:', updatedPayments)
    console.log('Payment status migration completed successfully!')

  } catch (error) {
    console.error('Error during payment status migration:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the migration
migratePaymentStatus()
  .catch((error) => {
    console.error('Migration failed:', error)
    process.exit(1)
  })

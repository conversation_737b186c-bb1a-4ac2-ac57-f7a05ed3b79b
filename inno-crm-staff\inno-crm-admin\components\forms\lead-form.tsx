'use client'

import { useState } from 'react'
import { useBranchSafe } from '@/contexts/branch-context'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

const leadSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  phone: z.string().min(9, 'Please enter a valid phone number'),
  coursePreference: z.string().min(1, 'Please select a course'),
})

type LeadFormData = z.infer<typeof leadSchema>

export function LeadForm() {
  // Use branch context if available, otherwise default to 'main'
  const branchContext = useBranchSafe()
  const currentBranch = branchContext?.currentBranch || { id: 'main' }

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<LeadFormData>({
    resolver: zodResolver(leadSchema),
  })

  const onSubmit = async (data: LeadFormData) => {
    setIsSubmitting(true)
    try {
      const response = await fetch('/api/leads', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ...data, branch: currentBranch.id }),
      })

      if (response.ok) {
        setIsSubmitted(true)
        reset()
      } else {
        throw new Error('Failed to submit form')
      }
    } catch (error) {
      console.error('Error submitting form:', error)
      alert('There was an error submitting your information. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isSubmitted) {
    return (
      <div className="text-center py-8">
        <div className="text-green-600 text-lg font-semibold mb-2">
          Thank you for your interest!
        </div>
        <p className="text-gray-600 mb-4">
          We&apos;ll contact you within 24 hours to discuss your English learning goals.
        </p>
        <Button onClick={() => setIsSubmitted(false)} variant="outline">
          Submit Another Request
        </Button>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div>
        <Label htmlFor="name">Full Name</Label>
        <Input
          id="name"
          {...register('name')}
          placeholder="Enter your full name"
          className="mt-1"
        />
        {errors.name && (
          <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="phone">Phone Number</Label>
        <Input
          id="phone"
          {...register('phone')}
          placeholder="+998 90 123 45 67"
          className="mt-1"
        />
        {errors.phone && (
          <p className="text-red-500 text-sm mt-1">{errors.phone.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="coursePreference">Course Interest</Label>
        <select
          id="coursePreference"
          {...register('coursePreference')}
          className="mt-1 block w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
        >
          <option value="">Select a course</option>
          <option value="General English">General English</option>
          <option value="IELTS Preparation">IELTS Preparation</option>
          <option value="SAT Preparation">SAT Preparation</option>
          <option value="Kids English">Kids English</option>
          <option value="Business English">Business English</option>
          <option value="Math Courses">Math Courses</option>
        </select>
        {errors.coursePreference && (
          <p className="text-red-500 text-sm mt-1">{errors.coursePreference.message}</p>
        )}
      </div>

      <Button type="submit" className="w-full" disabled={isSubmitting}>
        {isSubmitting ? 'Submitting...' : 'Get Free Consultation'}
      </Button>
    </form>
  )
}

import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import bcrypt from 'bcryptjs'

const userCreateSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  phone: z.string().min(9, 'Phone number must be at least 9 characters'),
  email: z.string().email().optional(),
  role: z.enum(['ADMIN', 'MANAGER', 'TEACHER', 'RECEPTION', 'CASHIER', 'STUDENT', 'ACADEMIC_MANAGER']),
  password: z.string().min(6, 'Password must be at least 6 characters'),
})

const userUpdateSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').optional(),
  phone: z.string().min(9, 'Phone number must be at least 9 characters').optional(),
  email: z.string().email().optional(),
  role: z.enum(['ADMIN', 'MANAGER', 'TEACHER', 'RECEPTION', 'CASHIER', 'STUDENT', 'ACADEMIC_MANAGER']).optional(),
  password: z.string().min(6, 'Password must be at least 6 characters').optional(),
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const role = searchParams.get('role') || ''

    const skip = (page - 1) * limit

    const whereClause: any = {}

    if (search) {
      whereClause.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { phone: { contains: search } },
        { email: { contains: search, mode: 'insensitive' } },
      ]
    }

    if (role) {
      whereClause.role = role
    }

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where: whereClause,
        skip,
        take: limit,
        select: {
          id: true,
          name: true,
          phone: true,
          email: true,
          role: true,
          createdAt: true,
          updatedAt: true,
          studentProfile: {
            select: {
              id: true,
              level: true,
              branch: true,
            },
          },
          teacherProfile: {
            select: {
              id: true,
              subject: true,
              experience: true,
              branch: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.user.count({ where: whereClause }),
    ])

    return NextResponse.json({
      users,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = userCreateSchema.parse(body)

    // Check if user with this phone already exists
    const existingUser = await prisma.user.findUnique({
      where: { phone: validatedData.phone },
    })

    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this phone number already exists' },
        { status: 400 }
      )
    }

    // Check if email is provided and already exists
    if (validatedData.email) {
      const existingEmail = await prisma.user.findUnique({
        where: { email: validatedData.email },
      })

      if (existingEmail) {
        return NextResponse.json(
          { error: 'User with this email already exists' },
          { status: 400 }
        )
      }
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(validatedData.password, 12)

    // Create user
    const user = await prisma.user.create({
      data: {
        name: validatedData.name,
        phone: validatedData.phone,
        email: validatedData.email,
        role: validatedData.role,
        password: hashedPassword,
      },
      select: {
        id: true,
        name: true,
        phone: true,
        email: true,
        role: true,
        createdAt: true,
        updatedAt: true,
      },
    })

    // Create profile based on role
    if (validatedData.role === 'STUDENT') {
      await prisma.student.create({
        data: {
          userId: user.id,
          level: 'A1', // Default level
          branch: 'Main', // Default branch
        },
      })
    } else if (validatedData.role === 'TEACHER') {
      await prisma.teacher.create({
        data: {
          userId: user.id,
          subject: 'English', // Default subject
          branch: 'Main', // Default branch
        },
      })
    }

    return NextResponse.json(user, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating user:', error)
    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, ...updateData } = body
    
    if (!id) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    const validatedData = userUpdateSchema.parse(updateData)

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id },
    })

    if (!existingUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Check for phone uniqueness if phone is being updated
    if (validatedData.phone && validatedData.phone !== existingUser.phone) {
      const phoneExists = await prisma.user.findUnique({
        where: { phone: validatedData.phone },
      })

      if (phoneExists) {
        return NextResponse.json(
          { error: 'Phone number already exists' },
          { status: 400 }
        )
      }
    }

    // Check for email uniqueness if email is being updated
    if (validatedData.email && validatedData.email !== existingUser.email) {
      const emailExists = await prisma.user.findUnique({
        where: { email: validatedData.email },
      })

      if (emailExists) {
        return NextResponse.json(
          { error: 'Email already exists' },
          { status: 400 }
        )
      }
    }

    // Prepare update data
    const updatePayload: any = {}
    
    if (validatedData.name) updatePayload.name = validatedData.name
    if (validatedData.phone) updatePayload.phone = validatedData.phone
    if (validatedData.email !== undefined) updatePayload.email = validatedData.email
    if (validatedData.role) updatePayload.role = validatedData.role
    
    if (validatedData.password) {
      updatePayload.password = await bcrypt.hash(validatedData.password, 12)
    }

    // Update user
    const updatedUser = await prisma.user.update({
      where: { id },
      data: updatePayload,
      select: {
        id: true,
        name: true,
        phone: true,
        email: true,
        role: true,
        createdAt: true,
        updatedAt: true,
      },
    })

    return NextResponse.json(updatedUser)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating user:', error)
    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id },
      include: {
        studentProfile: true,
        teacherProfile: true,
      }
    })

    if (!existingUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Use transaction to handle cascading deletes properly
    await prisma.$transaction(async (tx) => {
      // If user is a student, delete related records first
      if (existingUser.studentProfile) {
        const studentId = existingUser.studentProfile.id

        // Delete payments
        await tx.payment.deleteMany({
          where: { studentId }
        })

        // Delete enrollments
        await tx.enrollment.deleteMany({
          where: { studentId }
        })

        // Delete attendance records
        await tx.attendance.deleteMany({
          where: { studentId }
        })

        // Delete assessments
        await tx.assessment.deleteMany({
          where: { studentId }
        })

        // Delete student profile
        await tx.student.delete({
          where: { id: studentId }
        })
      }

      // If user is a teacher, handle teacher-related records
      if (existingUser.teacherProfile) {
        const teacherId = existingUser.teacherProfile.id

        // Update groups to remove teacher assignment (set to null)
        await tx.group.updateMany({
          where: { teacherId },
          data: { teacherId: null as any }
        })

        // Delete teacher profile
        await tx.teacher.delete({
          where: { id: teacherId }
        })
      }

      // Finally, delete the user
      await tx.user.delete({
        where: { id }
      })
    })

    return NextResponse.json({ message: 'User deleted successfully' })
  } catch (error) {
    console.error('Error deleting user:', error)
    return NextResponse.json(
      { error: 'Failed to delete user' },
      { status: 500 }
    )
  }
}

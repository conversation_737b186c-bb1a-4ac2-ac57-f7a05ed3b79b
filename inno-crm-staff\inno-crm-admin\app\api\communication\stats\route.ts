import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has permission to view communication stats
    if (!session.user.role || !['ADMIN', 'MANAGER', 'RECEPTION'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || 'today' // today, week, month

    let startDate: Date
    const endDate = new Date()

    switch (period) {
      case 'week':
        startDate = new Date()
        startDate.setDate(startDate.getDate() - 7)
        break
      case 'month':
        startDate = new Date()
        startDate.setMonth(startDate.getMonth() - 1)
        break
      default: // today
        startDate = new Date()
        startDate.setHours(0, 0, 0, 0)
        break
    }

    // Get call records for the period
    const callRecords = await prisma.callRecord.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        user: {
          select: {
            name: true,
          },
        },
        lead: {
          select: {
            name: true,
            phone: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    })

    // Get activity logs for SMS and email tracking
    const communicationLogs = await prisma.activityLog.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
        OR: [
          { action: 'SEND_SMS' },
          { action: 'SEND_EMAIL' },
          { action: 'SEND_NOTIFICATION' },
        ],
      },
      include: {
        user: {
          select: {
            name: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    })

    // Calculate statistics
    const stats = {
      calls: {
        total: callRecords.length,
        successful: callRecords.filter(call => call.duration && call.duration > 30).length,
        averageDuration: callRecords.length > 0 
          ? callRecords.reduce((sum, call) => sum + (call.duration || 0), 0) / callRecords.length
          : 0,
      },
      sms: {
        total: communicationLogs.filter(log => log.action === 'SEND_SMS').length,
        delivered: Math.floor(communicationLogs.filter(log => log.action === 'SEND_SMS').length * 0.95), // Mock 95% delivery rate
      },
      email: {
        total: communicationLogs.filter(log => log.action === 'SEND_EMAIL').length,
        delivered: Math.floor(communicationLogs.filter(log => log.action === 'SEND_EMAIL').length * 0.92), // Mock 92% delivery rate
      },
      notifications: {
        total: communicationLogs.filter(log => log.action === 'SEND_NOTIFICATION').length,
        active: communicationLogs.filter(log =>
          log.action === 'SEND_NOTIFICATION' &&
          new Date(log.createdAt) > new Date(Date.now() - 24 * 60 * 60 * 1000)
        ).length, // Active notifications in last 24 hours
      },
    }

    // Get recent communication activities
    const recentActivities = []

    // Add recent calls
    for (const call of callRecords.slice(0, 5)) {
      recentActivities.push({
        id: call.id,
        type: 'CALL',
        recipient: call.lead?.name || 'Unknown',
        message: `Call duration: ${call.duration ? Math.floor(call.duration / 60) : 0} minutes`,
        status: call.duration && call.duration > 30 ? 'Completed' : 'Missed',
        timestamp: call.createdAt,
        user: call.user.name,
      })
    }

    // Add recent SMS/Email from logs
    for (const log of communicationLogs.slice(0, 5)) {
      recentActivities.push({
        id: log.id,
        type: log.action === 'SEND_SMS' ? 'SMS' : log.action === 'SEND_EMAIL' ? 'EMAIL' : 'NOTIFICATION',
        recipient: typeof log.details === 'string' && log.details.includes('to:') ? log.details.split('to:')[1]?.split(' ')[0] : 'Multiple recipients',
        message: typeof log.details === 'string' ? log.details : 'Communication sent',
        status: 'Delivered',
        timestamp: log.createdAt,
        user: log.user.name,
      })
    }

    // Sort by timestamp
    recentActivities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())

    // Get top performers (users with most communication activities)
    const userStats = new Map()
    
    callRecords.forEach(call => {
      const userId = call.userId
      if (!userStats.has(userId)) {
        userStats.set(userId, { 
          name: call.user.name, 
          calls: 0, 
          messages: 0, 
          totalActivities: 0 
        })
      }
      const stats = userStats.get(userId)
      stats.calls++
      stats.totalActivities++
    })

    communicationLogs.forEach(log => {
      const userId = log.userId
      if (!userStats.has(userId)) {
        userStats.set(userId, { 
          name: log.user.name, 
          calls: 0, 
          messages: 0, 
          totalActivities: 0 
        })
      }
      const stats = userStats.get(userId)
      stats.messages++
      stats.totalActivities++
    })

    const topPerformers = Array.from(userStats.values())
      .sort((a, b) => b.totalActivities - a.totalActivities)
      .slice(0, 5)

    const communicationStats = {
      period,
      dateRange: {
        start: startDate.toISOString(),
        end: endDate.toISOString(),
      },
      stats,
      recentActivities: recentActivities.slice(0, 10),
      topPerformers,
      trends: await calculateGrowthTrends(startDate, endDate),
    }

    return NextResponse.json(communicationStats)
  } catch (error) {
    console.error('Error fetching communication stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function calculateGrowthTrends(currentStart: Date, currentEnd: Date) {
  // Calculate previous period dates
  const periodDuration = currentEnd.getTime() - currentStart.getTime()
  const previousStart = new Date(currentStart.getTime() - periodDuration)
  const previousEnd = new Date(currentStart.getTime())

  // Get current period data
  const [currentCalls, currentSms, currentEmail] = await Promise.all([
    prisma.callRecord.count({
      where: {
        createdAt: { gte: currentStart, lte: currentEnd }
      }
    }),
    prisma.activityLog.count({
      where: {
        action: 'SEND_SMS',
        createdAt: { gte: currentStart, lte: currentEnd }
      }
    }),
    prisma.activityLog.count({
      where: {
        action: 'SEND_EMAIL',
        createdAt: { gte: currentStart, lte: currentEnd }
      }
    })
  ])

  // Get previous period data
  const [previousCalls, previousSms, previousEmail] = await Promise.all([
    prisma.callRecord.count({
      where: {
        createdAt: { gte: previousStart, lte: previousEnd }
      }
    }),
    prisma.activityLog.count({
      where: {
        action: 'SEND_SMS',
        createdAt: { gte: previousStart, lte: previousEnd }
      }
    }),
    prisma.activityLog.count({
      where: {
        action: 'SEND_EMAIL',
        createdAt: { gte: previousStart, lte: previousEnd }
      }
    })
  ])

  // Calculate growth percentages
  const calculateGrowth = (current: number, previous: number) => {
    if (previous === 0) return current > 0 ? 100 : 0
    return Math.round(((current - previous) / previous) * 100)
  }

  return {
    callsGrowth: calculateGrowth(currentCalls, previousCalls),
    smsGrowth: calculateGrowth(currentSms, previousSms),
    emailGrowth: calculateGrowth(currentEmail, previousEmail),
  }
}

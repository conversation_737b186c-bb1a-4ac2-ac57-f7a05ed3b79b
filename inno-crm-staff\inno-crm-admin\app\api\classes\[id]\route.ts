import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ActivityLogger } from '@/lib/activity-logger'
import { Role } from '@prisma/client'
import * as z from 'zod'

const updateClassSchema = z.object({
  groupId: z.string().optional(),
  teacherId: z.string().optional(),
  date: z.string().optional(),
  topic: z.string().optional(),
  homework: z.string().optional(),
  notes: z.string().optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    const classRecord = await prisma.class.findUnique({
      where: { id },
      include: {
        group: {
          include: {
            course: {
              select: {
                id: true,
                name: true,
                level: true,
                description: true,
              },
            },
            enrollments: {
              include: {
                student: {
                  include: {
                    user: {
                      select: {
                        id: true,
                        name: true,
                        phone: true,
                      },
                    },
                  },
                },
              },
              where: { status: 'ACTIVE' },
            },
          },
        },
        teacher: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                phone: true,
                email: true,
              },
            },
          },
        },
        attendances: {
          include: {
            student: {
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
      },
    })

    if (!classRecord) {
      return NextResponse.json({ error: 'Class not found' }, { status: 404 })
    }

    return NextResponse.json(classRecord)
  } catch (error) {
    console.error('Error fetching class:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only teachers, admins, and managers can update classes
    if (!session.user.role || !['ADMIN', 'MANAGER', 'TEACHER'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { id } = await params
    const body = await request.json()
    const validatedData = updateClassSchema.parse(body)

    // Check if class exists
    const existingClass = await prisma.class.findUnique({
      where: { id },
      include: {
        group: {
          select: {
            name: true,
          },
        },
        teacher: {
          include: {
            user: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    })

    if (!existingClass) {
      return NextResponse.json({ error: 'Class not found' }, { status: 404 })
    }

    // If groupId is being updated, check if group exists
    if (validatedData.groupId && validatedData.groupId !== existingClass.groupId) {
      const group = await prisma.group.findUnique({
        where: { id: validatedData.groupId },
      })

      if (!group) {
        return NextResponse.json(
          { error: 'Group not found' },
          { status: 400 }
        )
      }
    }

    // If teacherId is being updated, check if teacher exists
    if (validatedData.teacherId && validatedData.teacherId !== existingClass.teacherId) {
      const teacher = await prisma.teacher.findUnique({
        where: { id: validatedData.teacherId },
      })

      if (!teacher) {
        return NextResponse.json(
          { error: 'Teacher not found' },
          { status: 400 }
        )
      }
    }

    // If date is being updated, check for conflicts
    if (validatedData.date) {
      const newDate = new Date(validatedData.date)
      const conflictingClass = await prisma.class.findFirst({
        where: {
          id: { not: id },
          groupId: validatedData.groupId || existingClass.groupId,
          date: newDate,
        },
      })

      if (conflictingClass) {
        return NextResponse.json(
          { error: 'A class already exists for this group on this date' },
          { status: 400 }
        )
      }
    }

    const updateData: any = { ...validatedData }
    if (validatedData.date) {
      updateData.date = new Date(validatedData.date)
    }

    const classRecord = await prisma.class.update({
      where: { id },
      data: updateData,
      include: {
        group: {
          include: {
            course: {
              select: {
                name: true,
                level: true,
              },
            },
          },
        },
        teacher: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        _count: {
          select: {
            attendances: true,
          },
        },
      },
    })

    // Log the activity
    await ActivityLogger.log({
      userId: session.user.id,
      userRole: session.user.role as Role,
      action: 'UPDATE',
      resource: 'class',
      resourceId: classRecord.id,
      details: {
        changes: validatedData,
        groupName: classRecord.group.name,
        courseName: classRecord.group.course?.name,
        teacherName: classRecord.teacher.user.name,
      },
      ipAddress: ActivityLogger.getIpAddress(request),
      userAgent: ActivityLogger.getUserAgent(request),
    })

    return NextResponse.json(classRecord)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating class:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only admins and managers can delete classes
    if (!session.user.role || !['ADMIN', 'MANAGER'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { id } = await params

    // Check if class exists
    const existingClass = await prisma.class.findUnique({
      where: { id },
      include: {
        group: {
          select: {
            name: true,
          },
        },
        teacher: {
          include: {
            user: {
              select: {
                name: true,
              },
            },
          },
        },
        _count: {
          select: {
            attendances: true,
          },
        },
      },
    })

    if (!existingClass) {
      return NextResponse.json({ error: 'Class not found' }, { status: 404 })
    }

    // Check if class has attendance records
    if (existingClass._count.attendances > 0) {
      return NextResponse.json(
        {
          error: 'Cannot delete class with attendance records',
          details: 'This class has attendance records. Please delete attendance records first or contact an administrator.'
        },
        { status: 400 }
      )
    }

    await prisma.class.delete({
      where: { id },
    })

    // Log the activity
    await ActivityLogger.log({
      userId: session.user.id,
      userRole: session.user.role as Role,
      action: 'DELETE',
      resource: 'class',
      resourceId: id,
      details: {
        groupName: existingClass.group.name,
        teacherName: existingClass.teacher.user.name,
        date: existingClass.date.toISOString(),
        topic: existingClass.topic,
      },
      ipAddress: ActivityLogger.getIpAddress(request),
      userAgent: ActivityLogger.getUserAgent(request),
    })

    return NextResponse.json({ message: 'Class deleted successfully' })
  } catch (error) {
    console.error('Error deleting class:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

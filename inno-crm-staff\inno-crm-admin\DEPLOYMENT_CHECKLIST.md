# Complete Deployment Checklist for Inno-CRM

This checklist ensures both Admin and Staff servers are properly deployed to Vercel.

## 📋 Pre-Deployment Preparation

### ✅ Staff Server (Current Repository) - Ready ✅
- [x] Repository configured as `inno-crm-staff`
- [x] Environment variables configured in `.env.production`
- [x] Vercel configuration optimized in `vercel.json`
- [x] Inter-server communication setup
- [x] Build script includes Prisma generation
- [x] Feature flags configured for staff roles
- [x] CORS configured for admin server communication

### 🔧 Admin Server Setup Required
- [ ] Create separate repository `inno-crm-admin`
- [ ] Copy and modify configuration files
- [ ] Set up admin database
- [ ] Configure environment variables
- [ ] Update feature flags for full access
- [ ] Test admin-specific functionality

## 🚀 Deployment Steps

### Step 1: Deploy Staff Server (Current Repository)

#### 1.1 Final Preparation
```bash
# Run deployment preparation script
npm run prepare-deployment

# Verify all files are ready
git status
git add .
git commit -m "Final staff server deployment preparation"
git push origin main
```

#### 1.2 Deploy to Vercel
1. **Go to Vercel Dashboard**: [vercel.com/new](https://vercel.com/new)
2. **Import Repository**: Select your GitHub repository
3. **Project Settings**:
   - Project Name: `inno-crm-staff`
   - Framework: Next.js (auto-detected)
   - Build Command: `npm run build` (auto-configured)
   - Install Command: `npm install` (auto-configured)

#### 1.3 Configure Environment Variables
Copy these variables to Vercel dashboard:

**Essential Variables:**
```env
DATABASE_URL=postgresql://crm-staff_owner:<EMAIL>/crm-staff?sslmode=require&channel_binding=require
NEXTAUTH_SECRET=inno-crm-staff-super-secret-key-for-production-2024-very-long-and-secure
NEXTAUTH_URL=https://inno-crm-staff.vercel.app
PRISMA_GENERATE_DATAPROXY=true
SERVER_TYPE=staff
ADMIN_SERVER_URL=https://inno-crm-admin.vercel.app
STAFF_SERVER_URL=https://inno-crm-staff.vercel.app
INTER_SERVER_SECRET=inno-crm-inter-server-communication-secret-2024
```

**Additional Variables** (copy all from `.env.production`):
- SMS configuration
- Email configuration
- Feature flags
- Business settings

#### 1.4 Deploy and Verify
1. Click **Deploy**
2. Wait for build completion
3. Test deployment: `https://inno-crm-staff.vercel.app`
4. Check health endpoint: `https://inno-crm-staff.vercel.app/api/health`

### Step 2: Create and Deploy Admin Server

#### 2.1 Create Admin Repository
```bash
# Clone current repository for admin version
git clone https://github.com/MrFarrukhT/inno-crm.git inno-crm-admin
cd inno-crm-admin

# Run admin setup script
npm run setup-admin-server

# Create new repository
rm -rf .git
git init
git remote add origin https://github.com/MrFarrukhT/inno-crm-admin.git
git add .
git commit -m "Initial admin server setup"
git push -u origin main
```

#### 2.2 Database Setup for Admin
**Option A: Create New Neon Database**
1. Go to [Neon Console](https://console.neon.tech/)
2. Create new project: `inno-crm-admin`
3. Copy connection string
4. Update `DATABASE_URL` in admin environment

**Option B: Use Existing Database with New Schema**
1. Create new database in existing Neon project
2. Update connection string to point to admin database

#### 2.3 Deploy Admin Server to Vercel
1. **Import Admin Repository**: [vercel.com/new](https://vercel.com/new)
2. **Project Settings**:
   - Project Name: `inno-crm-admin`
   - Framework: Next.js
3. **Environment Variables** (use `.env.production.admin`):
```env
DATABASE_URL=your-admin-database-url
NEXTAUTH_SECRET=inno-crm-admin-super-secret-key-for-production-2024-very-long-and-secure
NEXTAUTH_URL=https://inno-crm-admin.vercel.app
PRISMA_GENERATE_DATAPROXY=true
SERVER_TYPE=admin
ADMIN_SERVER_URL=https://inno-crm-admin.vercel.app
STAFF_SERVER_URL=https://inno-crm-staff.vercel.app
INTER_SERVER_SECRET=inno-crm-inter-server-communication-secret-2024
# ... all other variables
```

#### 2.4 Initialize Admin Database
```bash
# In admin repository
npm install
npx prisma generate
npx prisma db push
npx prisma db seed
```

## 🧪 Testing and Verification

### Staff Server Testing
- [ ] **Access**: `https://inno-crm-staff.vercel.app`
- [ ] **Health Check**: `https://inno-crm-staff.vercel.app/api/health`
- [ ] **Login**: Test with reception/teacher accounts
- [ ] **Features**: Verify limited feature access
- [ ] **Database**: Check database connectivity
- [ ] **Inter-server**: Test communication endpoints

### Admin Server Testing
- [ ] **Access**: `https://inno-crm-admin.vercel.app`
- [ ] **Health Check**: `https://inno-crm-admin.vercel.app/api/health`
- [ ] **Login**: Test with admin/cashier accounts
- [ ] **Features**: Verify full feature access
- [ ] **Analytics**: Check analytics dashboard
- [ ] **Payments**: Test payment management
- [ ] **Inter-server**: Test communication with staff server

### Inter-Server Communication Testing
```bash
# Test staff to admin communication
curl -X GET https://inno-crm-admin.vercel.app/api/inter-server/health \
  -H "X-Inter-Server-Secret: your-secret" \
  -H "User-Agent: staff-server"

# Test admin to staff communication
curl -X GET https://inno-crm-staff.vercel.app/api/inter-server/health \
  -H "X-Inter-Server-Secret: your-secret" \
  -H "User-Agent: admin-server"
```

## 🔐 Security Verification

### Access Control
- [ ] Staff server blocks admin/cashier roles
- [ ] Admin server allows all roles
- [ ] Inter-server authentication working
- [ ] CORS configured correctly

### Environment Security
- [ ] Different NEXTAUTH_SECRET for each server
- [ ] Same INTER_SERVER_SECRET for both servers
- [ ] Database URLs point to correct databases
- [ ] No secrets exposed in client-side code

## 📊 Monitoring Setup

### Health Monitoring
- [ ] Set up Vercel monitoring
- [ ] Configure uptime monitoring
- [ ] Set up error tracking
- [ ] Monitor database connections

### Performance Monitoring
- [ ] Check build times
- [ ] Monitor response times
- [ ] Track memory usage
- [ ] Monitor function execution times

## 🚨 Troubleshooting Guide

### Common Deployment Issues

#### Build Failures
```bash
# Check build logs in Vercel dashboard
# Common fixes:
npm install
npx prisma generate
npm run build
```

#### Database Connection Issues
- Verify DATABASE_URL format
- Check database accessibility from Vercel
- Ensure PRISMA_GENERATE_DATAPROXY=true

#### Authentication Issues
- Verify NEXTAUTH_URL matches deployment URL
- Check NEXTAUTH_SECRET is set
- Ensure session configuration is correct

#### Inter-Server Communication Issues
- Verify INTER_SERVER_SECRET matches
- Check CORS configuration
- Ensure server URLs are correct

## 📝 Post-Deployment Tasks

### Documentation Updates
- [ ] Update team with new URLs
- [ ] Document access credentials
- [ ] Update API documentation
- [ ] Create user guides

### Team Notification
- [ ] **Staff Server**: `https://inno-crm-staff.vercel.app`
- [ ] **Admin Server**: `https://inno-crm-admin.vercel.app`
- [ ] Distribute login credentials
- [ ] Provide training on new system

### Ongoing Maintenance
- [ ] Set up automated backups
- [ ] Configure monitoring alerts
- [ ] Plan regular updates
- [ ] Schedule security reviews

## ✅ Deployment Success Criteria

### Staff Server Success
- [x] Deployed to Vercel successfully
- [ ] Health check returns 200
- [ ] Staff users can log in
- [ ] Limited features accessible
- [ ] Database operations working
- [ ] Inter-server communication functional

### Admin Server Success
- [ ] Deployed to Vercel successfully
- [ ] Health check returns 200
- [ ] Admin users can log in
- [ ] All features accessible
- [ ] Analytics dashboard working
- [ ] Payment management working
- [ ] Inter-server communication functional

### Integration Success
- [ ] Both servers communicate successfully
- [ ] User authentication works across servers
- [ ] Data synchronization functional
- [ ] Role-based access enforced
- [ ] Security measures in place

## 🎉 Completion

When all items are checked:
1. Both servers are successfully deployed
2. Inter-server communication is working
3. Security measures are in place
4. Team has been notified and trained
5. Monitoring is active

**Congratulations! Your Inno-CRM dual-server deployment is complete!** 🚀

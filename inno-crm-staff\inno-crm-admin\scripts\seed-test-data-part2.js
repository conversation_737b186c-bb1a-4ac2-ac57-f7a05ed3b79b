const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

// Helper functions
const randomDate = (start, end) => {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()))
}

const randomPhone = () => {
  const prefixes = ['+998901', '+998902', '+998903', '+998904', '+998905', '+998906', '+998907', '+998908', '+998909']
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)]
  const number = Math.floor(Math.random() * 9000000) + 1000000
  return `${prefix}${number}`
}

const firstNames = [
  '<PERSON>k<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>',
  '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'
]

con<PERSON> last<PERSON><PERSON><PERSON> = [
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Otajonov', 'Pulatov', 'Qosimov',
  'Rahimov', 'Saidov', 'Toshmatov', 'Umarov', 'Valiyev', 'Yusupov', 'Zokirov', 'Alimov', 'Botirov'
]

const branches = ['Main Branch', 'Branch']
const levels = ['A1', 'A2', 'B1', 'B2', 'IELTS', 'SAT', 'MATH', 'KIDS']
const schedules = [
  'Monday, Wednesday, Friday - 9:00-11:00',
  'Monday, Wednesday, Friday - 11:00-13:00',
  'Monday, Wednesday, Friday - 14:00-16:00',
  'Monday, Wednesday, Friday - 16:00-18:00',
  'Monday, Wednesday, Friday - 18:00-20:00',
  'Tuesday, Thursday, Saturday - 9:00-11:00',
  'Tuesday, Thursday, Saturday - 11:00-13:00',
  'Tuesday, Thursday, Saturday - 14:00-16:00',
  'Tuesday, Thursday, Saturday - 16:00-18:00',
  'Tuesday, Thursday, Saturday - 18:00-20:00'
]

async function generateGroups() {
  console.log('Creating groups...')
  
  const teachers = await prisma.teacher.findMany({ include: { user: true } })
  const courses = await prisma.course.findMany()
  const groups = []

  // Create 15 groups with different teachers and schedules
  for (let i = 0; i < 15; i++) {
    const teacher = teachers[Math.floor(Math.random() * teachers.length)]
    const course = courses[Math.floor(Math.random() * courses.length)]
    const schedule = schedules[Math.floor(Math.random() * schedules.length)]
    const startDate = randomDate(new Date(2024, 0, 1), new Date(2024, 11, 31))
    const endDate = new Date(startDate.getTime() + (course.duration * 7 * 24 * 60 * 60 * 1000)) // weeks to milliseconds

    groups.push({
      name: `${course.level}-${teacher.user.name.split(' ')[0]}-${String(i + 1).padStart(2, '0')}`,
      courseId: course.id,
      teacherId: teacher.id,
      capacity: Math.floor(Math.random() * 10) + 10, // 10-20 capacity
      schedule: schedule,
      room: `Room ${Math.floor(Math.random() * 20) + 101}`,
      branch: teacher.branch,
      startDate: startDate,
      endDate: endDate,
      isActive: Math.random() > 0.2 // 80% active groups
    })
  }

  const createdGroups = await prisma.group.createMany({
    data: groups,
    skipDuplicates: true
  })

  console.log(`Created ${createdGroups.count} groups`)
  return await prisma.group.findMany({ include: { teacher: { include: { user: true } }, course: true } })
}

async function generateStudents() {
  console.log('Creating students...')
  
  const studentUsers = await prisma.user.findMany({ where: { role: 'STUDENT' } })
  const students = []

  const statuses = ['ACTIVE', 'DROPPED', 'PAUSED', 'COMPLETED']
  const statusWeights = [0.6, 0.2, 0.1, 0.1] // 60% active, 20% dropped, 10% paused, 10% completed

  for (const user of studentUsers) {
    // Weighted random status selection
    const rand = Math.random()
    let status = 'ACTIVE'
    let cumulative = 0
    for (let i = 0; i < statuses.length; i++) {
      cumulative += statusWeights[i]
      if (rand <= cumulative) {
        status = statuses[i]
        break
      }
    }

    const level = levels[Math.floor(Math.random() * levels.length)]
    const branch = branches[Math.floor(Math.random() * branches.length)]
    
    const studentData = {
      userId: user.id,
      level: level,
      branch: branch,
      status: status,
      emergencyContact: randomPhone(),
      dateOfBirth: randomDate(new Date(1990, 0, 1), new Date(2005, 11, 31)),
      address: `${Math.floor(Math.random() * 100) + 1} ${['Tashkent', 'Chilanzar', 'Yunusabad', 'Sergeli'][Math.floor(Math.random() * 4)]} District`
    }

    // Add status-specific fields
    if (status === 'DROPPED') {
      studentData.droppedAt = randomDate(new Date(2024, 0, 1), new Date())
      studentData.reEnrollmentNotes = 'Dropped due to personal reasons. Potential for re-enrollment.'
      studentData.lastContactedAt = randomDate(new Date(2024, 6, 1), new Date())
    } else if (status === 'PAUSED') {
      studentData.pausedAt = randomDate(new Date(2024, 6, 1), new Date())
    }

    students.push(studentData)
  }

  const createdStudents = await prisma.student.createMany({
    data: students,
    skipDuplicates: true
  })

  console.log(`Created ${createdStudents.count} students`)
  return await prisma.student.findMany({ include: { user: true } })
}

async function generateEnrollments(students, groups) {
  console.log('Creating enrollments...')
  
  const enrollments = []
  const activeStudents = students.filter(s => s.status === 'ACTIVE')
  
  // Enroll 70% of active students in groups
  const studentsToEnroll = activeStudents.slice(0, Math.floor(activeStudents.length * 0.7))
  
  for (const student of studentsToEnroll) {
    // Find groups in the same branch and appropriate level
    const suitableGroups = groups.filter(g => 
      g.branch === student.branch && 
      g.isActive &&
      (g.course.level === student.level || 
       (student.level === 'IELTS' && ['B1', 'B2'].includes(g.course.level)) ||
       (student.level === 'SAT' && ['B2', 'IELTS'].includes(g.course.level)))
    )
    
    if (suitableGroups.length > 0) {
      const group = suitableGroups[Math.floor(Math.random() * suitableGroups.length)]
      
      const enrollmentDate = randomDate(group.startDate, new Date())

      enrollments.push({
        studentId: student.id,
        groupId: group.id,
        startDate: enrollmentDate,
        status: 'ACTIVE'
      })

      // Update student's current group
      await prisma.student.update({
        where: { id: student.id },
        data: { currentGroupId: group.id }
      })
    }
  }

  if (enrollments.length > 0) {
    const createdEnrollments = await prisma.enrollment.createMany({
      data: enrollments,
      skipDuplicates: true
    })
    console.log(`Created ${createdEnrollments.count} enrollments`)
  }
}

async function generatePayments(students) {
  console.log('Creating payments...')
  
  const payments = []
  const paymentMethods = ['CASH', 'CARD']
  const paymentStatuses = ['PAID', 'DEBT', 'REFUNDED']
  const statusWeights = [0.7, 0.2, 0.1] // 70% paid, 20% debt, 10% refunded

  // Generate 30+ payment records
  for (let i = 0; i < 35; i++) {
    const student = students[Math.floor(Math.random() * students.length)]
    
    // Weighted random status selection
    const rand = Math.random()
    let status = 'PAID'
    let cumulative = 0
    for (let j = 0; j < paymentStatuses.length; j++) {
      cumulative += statusWeights[j]
      if (rand <= cumulative) {
        status = paymentStatuses[j]
        break
      }
    }

    const method = paymentMethods[Math.floor(Math.random() * paymentMethods.length)]
    const amount = Math.floor(Math.random() * 1000000) + 500000 // 500K to 1.5M UZS
    const createdDate = randomDate(new Date(2024, 0, 1), new Date())
    
    const paymentData = {
      studentId: student.id,
      amount: amount,
      method: method,
      status: status,
      description: `Monthly payment for ${student.level} course`,
      createdAt: createdDate
    }

    if (status === 'PAID') {
      paymentData.paidDate = createdDate
    }

    payments.push(paymentData)
  }

  const createdPayments = await prisma.payment.createMany({
    data: payments,
    skipDuplicates: true
  })

  console.log(`Created ${createdPayments.count} payments`)
}

async function main() {
  try {
    console.log('🌱 Starting Phase 2: Groups, Students, Enrollments, and Payments...')
    
    const groups = await generateGroups()
    const students = await generateStudents()
    await generateEnrollments(students, groups)
    await generatePayments(students)
    
    console.log('✅ Phase 2 complete: Groups, Students, Enrollments, and Payments created')
    
  } catch (error) {
    console.error('❌ Error in Phase 2:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })

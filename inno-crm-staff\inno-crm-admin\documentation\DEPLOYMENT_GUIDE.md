# CRM System - Deployment Guide

## 🚀 PRODUCTION DEPLOYMENT GUIDE

This guide provides comprehensive instructions for deploying the Innovative Centre CRM system to production.

## 📋 PRE-DEPLOYMENT CHECKLIST

### ✅ Environment Setup
- [ ] PostgreSQL database provisioned and accessible
- [ ] Environment variables configured
- [ ] SSL certificates configured (for HTTPS)
- [ ] Domain name configured and DNS pointing to server
- [ ] Backup strategy implemented

### ✅ Code Preparation
- [ ] All features tested in development
- [ ] Database schema applied (`npx prisma db push`)
- [ ] Prisma client generated (`npx prisma generate`)
- [ ] Production build tested (`npm run build`)
- [ ] Environment-specific configurations updated

---

## 🌐 VERCEL DEPLOYMENT (RECOMMENDED)

### Step 1: Prepare Repository
```bash
# Ensure all changes are committed
git add .
git commit -m "Production ready - CRM improvements implemented"
git push origin main
```

### Step 2: Vercel Configuration
Create or update `vercel.json`:
```json
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "env": {
    "DATABASE_URL": "@database_url",
    "NEXTAUTH_SECRET": "@nextauth_secret",
    "NEXTAUTH_URL": "@nextauth_url"
  }
}
```

### Step 3: Environment Variables
Set the following environment variables in Vercel dashboard:

```bash
# Database
DATABASE_URL="postgresql://username:password@host:port/database?sslmode=require"

# Authentication
NEXTAUTH_SECRET="your-production-secret-key-32-characters-minimum"
NEXTAUTH_URL="https://your-domain.com"

# SMS Providers (Optional)
ESKIZ_EMAIL="your-email"
ESKIZ_PASSWORD="your-password"

# File Upload (Optional)
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME="your-cloud-name"
```

### Step 4: Deploy to Vercel
```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy
vercel --prod
```

---

## 🐳 DOCKER DEPLOYMENT

### Step 1: Create Dockerfile
```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Build application
RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

### Step 2: Create docker-compose.yml
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - NEXTAUTH_URL=${NEXTAUTH_URL}
    depends_on:
      - postgres

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=crm
      - POSTGRES_USER=crm_user
      - POSTGRES_PASSWORD=secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
```

### Step 3: Deploy with Docker
```bash
# Build and run
docker-compose up -d

# Apply database migrations
docker-compose exec app npx prisma db push
```

---

## 🖥️ VPS/SERVER DEPLOYMENT

### Step 1: Server Setup
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 for process management
sudo npm install -g pm2

# Install Nginx for reverse proxy
sudo apt install nginx -y
```

### Step 2: Application Setup
```bash
# Clone repository
git clone https://github.com/your-username/inno-crm.git
cd inno-crm

# Install dependencies
npm install

# Generate Prisma client
npx prisma generate

# Build application
npm run build

# Apply database migrations
npx prisma db push
```

### Step 3: PM2 Configuration
Create `ecosystem.config.js`:
```javascript
module.exports = {
  apps: [{
    name: 'inno-crm',
    script: 'npm',
    args: 'start',
    cwd: '/path/to/inno-crm',
    env: {
      NODE_ENV: 'production',
      PORT: 3000,
      DATABASE_URL: 'your-database-url',
      NEXTAUTH_SECRET: 'your-secret',
      NEXTAUTH_URL: 'https://your-domain.com'
    }
  }]
}
```

### Step 4: Start Application
```bash
# Start with PM2
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 startup
pm2 startup
```

### Step 5: Nginx Configuration
Create `/etc/nginx/sites-available/inno-crm`:
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/inno-crm /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

---

## 🔒 SSL CERTIFICATE SETUP

### Using Certbot (Let's Encrypt)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal setup
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

---

## 📊 DATABASE SETUP

### PostgreSQL Configuration
```sql
-- Create database and user
CREATE DATABASE crm;
CREATE USER crm_user WITH ENCRYPTED PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE crm TO crm_user;

-- Connect to database
\c crm

-- Grant schema permissions
GRANT ALL ON SCHEMA public TO crm_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO crm_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO crm_user;
```

### Database Migration
```bash
# Apply schema
npx prisma db push

# Seed initial data (optional)
npx prisma db seed
```

---

## 🔧 MONITORING & MAINTENANCE

### Health Checks
Create health check endpoints and monitoring:
```bash
# Check application status
curl https://your-domain.com/api/health

# Monitor with PM2
pm2 monit

# Check logs
pm2 logs inno-crm
```

### Backup Strategy
```bash
# Database backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h localhost -U crm_user crm > backup_$DATE.sql

# Schedule daily backups
0 2 * * * /path/to/backup-script.sh
```

### Log Rotation
```bash
# Setup log rotation for PM2
pm2 install pm2-logrotate
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
```

---

## 🚨 TROUBLESHOOTING

### Common Issues

1. **Database Connection Failed**
   ```bash
   # Check database connectivity
   psql -h hostname -U username -d database_name
   
   # Verify environment variables
   echo $DATABASE_URL
   ```

2. **Build Failures**
   ```bash
   # Clear cache and rebuild
   rm -rf .next node_modules
   npm install
   npm run build
   ```

3. **Permission Errors**
   ```bash
   # Fix file permissions
   sudo chown -R $USER:$USER /path/to/app
   chmod -R 755 /path/to/app
   ```

4. **Memory Issues**
   ```bash
   # Increase Node.js memory limit
   export NODE_OPTIONS="--max-old-space-size=4096"
   ```

---

## ✅ POST-DEPLOYMENT CHECKLIST

- [ ] Application accessible via domain
- [ ] SSL certificate working
- [ ] Database connections working
- [ ] All API endpoints responding
- [ ] Authentication system working
- [ ] Activity logging functional
- [ ] KPI dashboard loading
- [ ] Assessment system operational
- [ ] Notification system working
- [ ] File uploads working (if configured)
- [ ] Backup system operational
- [ ] Monitoring alerts configured

---

## 📞 SUPPORT

For deployment issues:
1. Check application logs: `pm2 logs inno-crm`
2. Check Nginx logs: `sudo tail -f /var/log/nginx/error.log`
3. Check database connectivity
4. Verify environment variables
5. Review this deployment guide

**Deployment Status**: 🟢 Ready for production deployment

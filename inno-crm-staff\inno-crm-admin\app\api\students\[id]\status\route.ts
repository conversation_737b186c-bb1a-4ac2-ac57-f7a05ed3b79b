import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ActivityLogger } from '@/lib/activity-logger'
import { Role } from '@prisma/client'
import * as z from 'zod'

const statusUpdateSchema = z.object({
  status: z.enum(['ACTIVE', 'DROPPED', 'PAUSED', 'COMPLETED']),
  currentGroupId: z.string().optional(),
  reEnrollmentNotes: z.string().optional(),
  reason: z.string().optional(),
})

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only allow certain roles to update student status
    if (!session.user.role || !['ADMIN', 'MANAGER', 'RECEPTION'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { id } = await params
    const body = await request.json()
    const validatedData = statusUpdateSchema.parse(body)

    // Check if student exists
    const existingStudent = await prisma.student.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            name: true,
          },
        },

      },
    })

    if (!existingStudent) {
      return NextResponse.json(
        { error: 'Student not found' },
        { status: 404 }
      )
    }

    // Prepare update data based on status
    const updateData: any = {
      status: validatedData.status,
      updatedAt: new Date(),
    }

    const now = new Date()

    switch (validatedData.status) {
      case 'DROPPED':
        updateData.droppedAt = now
        updateData.currentGroupId = null // Remove from current group
        updateData.reEnrollmentNotes = validatedData.reEnrollmentNotes
        break
      
      case 'PAUSED':
        updateData.pausedAt = now
        // Keep current group for when they resume
        break
      
      case 'ACTIVE':
        updateData.resumedAt = now
        updateData.droppedAt = null
        updateData.pausedAt = null
        if (validatedData.currentGroupId) {
          updateData.currentGroupId = validatedData.currentGroupId
        }
        break
      
      case 'COMPLETED':
        updateData.currentGroupId = null
        break
    }

    // Update student status
    const updatedStudent = await prisma.student.update({
      where: { id },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            phone: true,
            email: true,
          },
        },

      },
    })

    // Log the activity
    await ActivityLogger.logStudentStatusChanged(
      session.user.id,
      session.user.role as Role,
      id,
      {
        studentName: existingStudent.user.name,
        oldStatus: existingStudent.status,
        newStatus: validatedData.status,
        reason: validatedData.reason,
        groupName: 'N/A',
      },
      request
    )

    return NextResponse.json(updatedStudent)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating student status:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

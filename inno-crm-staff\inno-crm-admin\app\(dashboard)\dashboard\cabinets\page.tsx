'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useBranch } from '@/contexts/branch-context'
import { 
  Search, Plus, Building, Users, MapPin, Edit, Trash2, Loader2, 
  Calendar, Clock, Package, Eye
} from 'lucide-react'
import CabinetForm from '@/components/forms/cabinet-form'
import Link from 'next/link'

interface Cabinet {
  id: string
  name: string
  number: string
  capacity: number
  floor?: number
  building?: string
  branch: string
  equipment?: string
  notes?: string
  isActive: boolean
  createdAt: string
  groups: Array<{
    id: string
    name: string
    course: {
      name: string
      level: string
    }
    teacher: {
      user: {
        name: string
      }
    }
  }>
  _count: {
    groups: number
    schedules: number
  }
}

export default function CabinetsPage() {
  const { currentBranch } = useBranch()
  const [cabinets, setCabinets] = useState<Cabinet[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingCabinet, setEditingCabinet] = useState<Cabinet | null>(null)

  const fetchCabinets = useCallback(async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        branch: currentBranch.id,
        ...(statusFilter !== 'all' && { isActive: statusFilter }),
        ...(searchTerm && { search: searchTerm }),
      })

      const response = await fetch(`/api/cabinets?${params}`)
      if (!response.ok) {
        throw new Error('Failed to fetch cabinets')
      }

      const data = await response.json()
      setCabinets(data.cabinets)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }, [currentBranch.id, statusFilter, searchTerm])

  useEffect(() => {
    fetchCabinets()
  }, [fetchCabinets])

  const handleCreateCabinet = async (data: any) => {
    const response = await fetch('/api/cabinets', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ ...data, branch: currentBranch.id }),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to create cabinet')
    }

    setIsCreateDialogOpen(false)
    fetchCabinets()
  }

  const handleEditCabinet = async (data: any) => {
    if (!editingCabinet) return

    const response = await fetch(`/api/cabinets/${editingCabinet.id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to update cabinet')
    }

    setIsEditDialogOpen(false)
    setEditingCabinet(null)
    fetchCabinets()
  }

  const handleDeleteCabinet = async (cabinetId: string) => {
    if (!confirm('Are you sure you want to delete this cabinet? This action cannot be undone.')) {
      return
    }

    try {
      const response = await fetch(`/api/cabinets/${cabinetId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete cabinet')
      }

      fetchCabinets()
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    }
  }

  const filteredCabinets = cabinets.filter(cabinet => {
    const matchesSearch = cabinet.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         cabinet.number.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         cabinet.building?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'true' && cabinet.isActive) ||
                         (statusFilter === 'false' && !cabinet.isActive)
    
    return matchesSearch && matchesStatus
  })

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Cabinets Management</h1>
          <p className="text-gray-600">Manage classroom cabinets and their schedules</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Cabinet
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add New Cabinet</DialogTitle>
              <DialogDescription>
                Create a new cabinet for classroom management.
              </DialogDescription>
            </DialogHeader>
            <CabinetForm
              onSubmit={handleCreateCabinet}
              onCancel={() => setIsCreateDialogOpen(false)}
              isEditing={false}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search cabinets..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Cabinets</SelectItem>
                <SelectItem value="true">Active Only</SelectItem>
                <SelectItem value="false">Inactive Only</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Cabinets Table */}
      <Card>
        <CardHeader>
          <CardTitle>Cabinets ({filteredCabinets.length})</CardTitle>
          <CardDescription>
            Manage classroom cabinets and their assignments
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Cabinet</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Capacity</TableHead>
                <TableHead>Groups</TableHead>
                <TableHead>Schedules</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredCabinets.map((cabinet) => (
                <TableRow key={cabinet.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{cabinet.name}</div>
                      <div className="text-sm text-gray-500">#{cabinet.number}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <MapPin className="h-3 w-3 text-gray-400" />
                      <span className="text-sm">
                        {cabinet.building && `${cabinet.building}, `}
                        {cabinet.floor !== undefined && `Floor ${cabinet.floor}`}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <Users className="h-3 w-3 text-gray-400" />
                      <span>{cabinet.capacity}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {cabinet._count.groups} groups
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {cabinet._count.schedules} schedules
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={cabinet.isActive ? 'default' : 'secondary'}>
                      {cabinet.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Link href={`/dashboard/cabinets/${cabinet.id}`}>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setEditingCabinet(cabinet)
                          setIsEditDialogOpen(true)
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-red-600 hover:text-red-700"
                        onClick={() => handleDeleteCabinet(cabinet.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredCabinets.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500">No cabinets found matching your criteria.</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Cabinet</DialogTitle>
            <DialogDescription>
              Update cabinet information and settings.
            </DialogDescription>
          </DialogHeader>
          {editingCabinet && (
            <CabinetForm
              initialData={editingCabinet}
              onSubmit={handleEditCabinet}
              onCancel={() => {
                setIsEditDialogOpen(false)
                setEditingCabinet(null)
              }}
              isEditing={true}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

// Inter-Server Data Synchronization Endpoint
// Handles data sync between admin and staff servers

import { NextRequest, NextResponse } from 'next/server';
import { validateInterServerAuth, InterServerUtils } from '@/lib/inter-server';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    // Validate inter-server authentication
    if (!validateInterServerAuth(request)) {
      InterServerUtils.logRequest('incoming', '/api/inter-server/sync', false, 'Unauthorized');
      return NextResponse.json(
        { error: 'Unauthorized inter-server request' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { type, data, operation = 'sync' } = body;

    if (!type || !data) {
      return NextResponse.json(
        { error: 'Type and data are required' },
        { status: 400 }
      );
    }

    let result;

    switch (type) {
      case 'user':
        result = await syncUser(data, operation);
        break;
      case 'student':
        result = await syncStudent(data, operation);
        break;
      case 'lead':
        result = await syncLead(data, operation);
        break;
      case 'enrollment':
        result = await syncEnrollment(data, operation);
        break;
      case 'attendance':
        result = await syncAttendance(data, operation);
        break;
      default:
        return NextResponse.json(
          { error: `Unsupported sync type: ${type}` },
          { status: 400 }
        );
    }

    InterServerUtils.logRequest('incoming', '/api/inter-server/sync', true, { type, operation });

    return NextResponse.json({
      success: true,
      type,
      operation,
      result,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Inter-server sync error:', error);
    InterServerUtils.logRequest('incoming', '/api/inter-server/sync', false, error);
    
    return NextResponse.json(
      { 
        error: 'Data synchronization failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Sync functions for different data types

async function syncUser(data: any, operation: string) {
  switch (operation) {
    case 'create':
      return await prisma.user.create({ data });
    case 'update':
      return await prisma.user.update({
        where: { id: data.id },
        data,
      });
    case 'sync':
      return await prisma.user.upsert({
        where: { id: data.id },
        create: data,
        update: data,
      });
    default:
      throw new Error(`Unsupported user operation: ${operation}`);
  }
}

async function syncStudent(data: any, operation: string) {
  switch (operation) {
    case 'create':
      return await prisma.student.create({ data });
    case 'update':
      return await prisma.student.update({
        where: { id: data.id },
        data,
      });
    case 'sync':
      return await prisma.student.upsert({
        where: { id: data.id },
        create: data,
        update: data,
      });
    default:
      throw new Error(`Unsupported student operation: ${operation}`);
  }
}

async function syncLead(data: any, operation: string) {
  switch (operation) {
    case 'create':
      return await prisma.lead.create({ data });
    case 'update':
      return await prisma.lead.update({
        where: { id: data.id },
        data,
      });
    case 'sync':
      return await prisma.lead.upsert({
        where: { id: data.id },
        create: data,
        update: data,
      });
    default:
      throw new Error(`Unsupported lead operation: ${operation}`);
  }
}

async function syncEnrollment(data: any, operation: string) {
  switch (operation) {
    case 'create':
      return await prisma.enrollment.create({ data });
    case 'update':
      return await prisma.enrollment.update({
        where: { id: data.id },
        data,
      });
    case 'sync':
      return await prisma.enrollment.upsert({
        where: { id: data.id },
        create: data,
        update: data,
      });
    default:
      throw new Error(`Unsupported enrollment operation: ${operation}`);
  }
}

async function syncAttendance(data: any, operation: string) {
  switch (operation) {
    case 'create':
      return await prisma.attendance.create({ data });
    case 'update':
      return await prisma.attendance.update({
        where: { id: data.id },
        data,
      });
    case 'sync':
      return await prisma.attendance.upsert({
        where: { id: data.id },
        create: data,
        update: data,
      });
    default:
      throw new Error(`Unsupported attendance operation: ${operation}`);
  }
}

export async function GET(request: NextRequest) {
  // Get sync status and statistics
  try {
    if (!validateInterServerAuth(request)) {
      return NextResponse.json(
        { error: 'Unauthorized inter-server request' },
        { status: 401 }
      );
    }

    // Return sync statistics
    const stats = {
      lastSync: new Date().toISOString(),
      server: process.env.SERVER_TYPE,
      syncEndpoints: [
        'user',
        'student', 
        'lead',
        'enrollment',
        'attendance'
      ],
      supportedOperations: ['create', 'update', 'sync'],
    };

    return NextResponse.json(stats);
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to get sync status' },
      { status: 500 }
    );
  }
}

'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Clock, Calendar, Users, Loader2 } from 'lucide-react'

const scheduleSchema = z.object({
  groupId: z.string().optional(),
  dayOfWeek: z.number().min(0).max(6),
  startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)'),
  endTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)'),
  title: z.string().optional(),
  isBlocked: z.boolean().default(false),
})

type ScheduleFormData = z.infer<typeof scheduleSchema>

interface ScheduleFormProps {
  cabinetId: string
  initialData?: Partial<ScheduleFormData>
  onSubmit: (data: ScheduleFormData) => Promise<void>
  onCancel?: () => void
  isEditing?: boolean
}

interface Group {
  id: string
  name: string
  course: {
    name: string
    level: string
  }
  teacher: {
    user: {
      name: string
    }
  }
}

const dayOptions = [
  { value: 0, label: 'Sunday' },
  { value: 1, label: 'Monday' },
  { value: 2, label: 'Tuesday' },
  { value: 3, label: 'Wednesday' },
  { value: 4, label: 'Thursday' },
  { value: 5, label: 'Friday' },
  { value: 6, label: 'Saturday' },
]

export default function CabinetScheduleForm({ 
  cabinetId, 
  initialData, 
  onSubmit, 
  onCancel, 
  isEditing = false 
}: ScheduleFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [groups, setGroups] = useState<Group[]>([])

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<ScheduleFormData>({
    resolver: zodResolver(scheduleSchema),
    defaultValues: {
      groupId: initialData?.groupId || '',
      dayOfWeek: initialData?.dayOfWeek ?? 1,
      startTime: initialData?.startTime || '09:00',
      endTime: initialData?.endTime || '11:00',
      title: initialData?.title || '',
      isBlocked: initialData?.isBlocked ?? false,
    },
  })

  const isBlocked = watch('isBlocked')
  const selectedGroupId = watch('groupId')

  useEffect(() => {
    fetchGroups()
  }, [])

  const fetchGroups = async () => {
    try {
      const response = await fetch('/api/groups')
      const data = await response.json()
      setGroups(data.groups || [])
    } catch (error) {
      console.error('Error fetching groups:', error)
    }
  }

  const handleFormSubmit = async (data: ScheduleFormData) => {
    try {
      setIsSubmitting(true)
      setError(null)

      // Validate time range
      if (data.startTime >= data.endTime) {
        throw new Error('End time must be after start time')
      }

      await onSubmit(data)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="space-y-4">
        {/* Schedule Type */}
        <div className="flex items-center space-x-2">
          <Switch
            id="isBlocked"
            checked={isBlocked}
            onCheckedChange={(checked) => setValue('isBlocked', checked)}
          />
          <Label htmlFor="isBlocked">Block Time (Maintenance/Other)</Label>
        </div>

        {/* Day and Time */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="dayOfWeek">Day of Week *</Label>
            <Select
              value={watch('dayOfWeek').toString()}
              onValueChange={(value) => setValue('dayOfWeek', parseInt(value))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select day" />
              </SelectTrigger>
              <SelectContent>
                {dayOptions.map((day) => (
                  <SelectItem key={day.value} value={day.value.toString()}>
                    {day.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.dayOfWeek && (
              <p className="text-sm text-red-600">{errors.dayOfWeek.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="startTime">Start Time *</Label>
            <div className="relative">
              <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                id="startTime"
                type="time"
                {...register('startTime')}
                className="pl-10"
              />
            </div>
            {errors.startTime && (
              <p className="text-sm text-red-600">{errors.startTime.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="endTime">End Time *</Label>
            <div className="relative">
              <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                id="endTime"
                type="time"
                {...register('endTime')}
                className="pl-10"
              />
            </div>
            {errors.endTime && (
              <p className="text-sm text-red-600">{errors.endTime.message}</p>
            )}
          </div>
        </div>

        {/* Group or Title */}
        {isBlocked ? (
          <div className="space-y-2">
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              {...register('title')}
              placeholder="e.g., Maintenance, Cleaning, etc."
            />
          </div>
        ) : (
          <div className="space-y-2">
            <Label htmlFor="groupId">Group</Label>
            <Select
              value={selectedGroupId}
              onValueChange={(value) => setValue('groupId', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select group (optional)" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">No group assigned</SelectItem>
                {groups.map((group) => (
                  <SelectItem key={group.id} value={group.id}>
                    {group.name} - {group.course.name} ({group.teacher.user.name})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-2 pt-6 border-t">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
        )}
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {isEditing ? 'Update Schedule' : 'Add Schedule'}
        </Button>
      </div>
    </form>
  )
}

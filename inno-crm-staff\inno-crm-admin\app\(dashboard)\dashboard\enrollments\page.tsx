'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { formatDate } from '@/lib/utils'
import { Search, Plus, User, GraduationCap, Calendar, CheckCircle, XCircle, Pause, Clock, Edit, Trash2, Loader2 } from 'lucide-react'
import EnrollmentForm from '@/components/forms/enrollment-form'
import { useBranch } from '@/contexts/branch-context'

interface Enrollment {
  id: string
  studentId: string
  groupId: string
  status: 'ACTIVE' | 'COMPLETED' | 'DROPPED' | 'SUSPENDED'
  startDate: string
  endDate: string | null
  createdAt: string
  student: {
    user: {
      id: string
      name: string
      phone: string
      email: string | null
    }
  }
  group: {
    id: string
    name: string
    course: {
      id: string
      name: string
      level: string
      duration: number
      price: number
    }
    teacher: {
      user: {
        name: string
      }
    }
  }
}

export default function EnrollmentsPage() {
  const { currentBranch } = useBranch()
  const [enrollments, setEnrollments] = useState<Enrollment[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('ALL')
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingEnrollment, setEditingEnrollment] = useState<Enrollment | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchEnrollments = useCallback(async () => {
    if (!currentBranch?.id) return

    try {
      const params = new URLSearchParams({
        branch: currentBranch.id,
        limit: '50'
      })

      if (statusFilter !== 'ALL') {
        params.append('status', statusFilter)
      }

      const url = `/api/enrollments?${params.toString()}`
      const response = await fetch(url)
      const data = await response.json()
      setEnrollments(data.enrollments || [])
    } catch (error) {
      console.error('Error fetching enrollments:', error)
    } finally {
      setLoading(false)
    }
  }, [statusFilter, currentBranch?.id])

  useEffect(() => {
    if (currentBranch?.id) {
      fetchEnrollments()
    }
  }, [fetchEnrollments, currentBranch?.id])

  const filteredEnrollments = enrollments.filter(enrollment =>
    enrollment.student.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    enrollment.student.user.phone.includes(searchTerm) ||
    enrollment.group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    enrollment.group.course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    enrollment.group.teacher.user.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4 text-blue-600" />
      case 'DROPPED':
        return <XCircle className="h-4 w-4 text-red-600" />
      case 'SUSPENDED':
        return <Pause className="h-4 w-4 text-yellow-600" />
      default:
        return null
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800'
      case 'COMPLETED':
        return 'bg-blue-100 text-blue-800'
      case 'DROPPED':
        return 'bg-red-100 text-red-800'
      case 'SUSPENDED':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('uz-UZ', {
      style: 'currency',
      currency: 'UZS',
      minimumFractionDigits: 0,
    }).format(amount)
  }

  // Calculate statistics
  const totalEnrollments = enrollments.length
  const activeCount = enrollments.filter(e => e.status === 'ACTIVE').length
  const completedCount = enrollments.filter(e => e.status === 'COMPLETED').length
  const droppedCount = enrollments.filter(e => e.status === 'DROPPED').length
  const suspendedCount = enrollments.filter(e => e.status === 'SUSPENDED').length

  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Enrollments Management - {currentBranch.name}</h1>
          <p className="text-gray-600">Manage student enrollments and course assignments for {currentBranch.name}</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Enrollment
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create New Enrollment</DialogTitle>
              <DialogDescription>
                Enroll a student in a course group.
              </DialogDescription>
            </DialogHeader>
            <EnrollmentForm
              onSubmit={async (data) => {
                // Handle enrollment creation
                try {
                  const response = await fetch('/api/enrollments', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                  })
                  if (response.ok) {
                    setIsCreateDialogOpen(false)
                    fetchEnrollments()
                  }
                } catch (error) {
                  console.error('Error creating enrollment:', error)
                }
              }}
              onCancel={() => setIsCreateDialogOpen(false)}
              isEditing={false}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Search & Filter</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search by student, group, course, or teacher..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Status</SelectItem>
                <SelectItem value="ACTIVE">Active</SelectItem>
                <SelectItem value="COMPLETED">Completed</SelectItem>
                <SelectItem value="DROPPED">Dropped</SelectItem>
                <SelectItem value="SUSPENDED">Suspended</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Enrollment Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <GraduationCap className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Enrollments</p>
                <p className="text-2xl font-bold text-gray-900">{totalEnrollments}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active</p>
                <p className="text-2xl font-bold text-gray-900">{activeCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-gray-900">{completedCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <XCircle className="h-8 w-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Dropped</p>
                <p className="text-2xl font-bold text-gray-900">{droppedCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Pause className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Suspended</p>
                <p className="text-2xl font-bold text-gray-900">{suspendedCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Enrollments Table */}
      <Card>
        <CardHeader>
          <CardTitle>Enrollments ({filteredEnrollments.length})</CardTitle>
          <CardDescription>
            Student course enrollments and their current status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Student</TableHead>
                <TableHead>Course</TableHead>
                <TableHead>Group</TableHead>
                <TableHead>Teacher</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Price</TableHead>
                <TableHead>Start Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredEnrollments.map((enrollment) => (
                <TableRow key={enrollment.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                          <User className="h-4 w-4 text-gray-600" />
                        </div>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {enrollment.student.user.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {enrollment.student.user.phone}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {enrollment.group.course.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        Level: {enrollment.group.course.level}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="font-medium">{enrollment.group.name}</span>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm">{enrollment.group.teacher.user.name}</span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1 text-gray-400" />
                      <span className="text-sm">{enrollment.group.course.duration} weeks</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm font-medium">
                      {formatCurrency(enrollment.group.course.price)}
                    </span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                      <span className="text-sm">{formatDate(enrollment.startDate)}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(enrollment.status)}>
                      <div className="flex items-center">
                        {getStatusIcon(enrollment.status)}
                        <span className="ml-1">{enrollment.status}</span>
                      </div>
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setEditingEnrollment(enrollment)
                          setIsEditDialogOpen(true)
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={async () => {
                          if (confirm('Are you sure you want to delete this enrollment?')) {
                            try {
                              const response = await fetch(`/api/enrollments/${enrollment.id}`, {
                                method: 'DELETE'
                              })
                              if (response.ok) {
                                fetchEnrollments()
                              }
                            } catch (error) {
                              console.error('Error deleting enrollment:', error)
                            }
                          }
                        }}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ActivityLogger } from '@/lib/activity-logger'
import { Role } from '@prisma/client'
import * as z from 'zod'

const messageSchema = z.object({
  subject: z.string().min(1, 'Subject is required'),
  content: z.string().min(1, 'Content is required'),
  recipientType: z.enum(['INDIVIDUAL', 'GROUP', 'ALL_STUDENTS', 'ALL_TEACHERS', 'ALL_ACADEMIC_MANAGERS']),
  recipientIds: z.array(z.string()).optional(),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).default('MEDIUM'),
  scheduledAt: z.string().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status')
    const priority = searchParams.get('priority')

    const skip = (page - 1) * limit

    // Build where clause for filtering
    const whereClause: any = {}

    if (status && status !== 'ALL') {
      whereClause.status = status
    }

    if (priority && priority !== 'ALL') {
      whereClause.priority = priority
    }

    const [messages, total] = await Promise.all([
      prisma.message.findMany({
        where: whereClause,
        include: {
          sender: {
            select: {
              name: true,
              email: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      prisma.message.count({ where: whereClause }),
    ])

    return NextResponse.json({
      messages,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching messages:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has permission to send messages
    if (!session.user.role || !['ADMIN', 'MANAGER', 'TEACHER'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = messageSchema.parse(body)

    // Create the message in the database
    const message = await prisma.message.create({
      data: {
        subject: validatedData.subject,
        content: validatedData.content,
        recipientType: validatedData.recipientType,
        recipientIds: validatedData.recipientIds || [],
        priority: validatedData.priority,
        status: 'SENT',
        sentAt: new Date(),
        senderId: session.user.id,
      },
      include: {
        sender: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    })

    // Log the activity
    await ActivityLogger.log({
      userId: session.user.id,
      userRole: (session.user.role as Role) || Role.ADMIN,
      action: 'CREATE',
      resource: 'MESSAGE',
      resourceId: message.id,
      details: `Created message: ${validatedData.subject}`,
    })

    return NextResponse.json(message, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating message:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

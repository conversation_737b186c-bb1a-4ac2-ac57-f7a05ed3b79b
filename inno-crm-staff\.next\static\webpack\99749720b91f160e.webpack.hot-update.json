{"c": ["webpack"], "r": ["app/layout", "app/page", "app/api/auth/[...nextauth]/route", "app/auth/signin/page", "app/(dashboard)/layout", "app/(dashboard)/dashboard/leads/page", "app/api/leads/route", "app/(dashboard)/dashboard/page"], "m": [null, "(app-pages-browser)/./app/globals.css", "(app-pages-browser)/./components/providers/auth-provider.tsx", "(app-pages-browser)/./components/providers/query-provider.tsx", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/OverloadYield.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/arrayLikeToArray.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/arrayWithHoles.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/assertThisInitialized.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/asyncToGenerator.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/classCallCheck.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/construct.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/createClass.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/defineProperty.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/getPrototypeOf.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/inherits.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/isNativeFunction.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/isNativeReflectConstruct.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/iterableToArrayLimit.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/nonIterableRest.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/possibleConstructorReturn.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/regenerator.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/regeneratorAsync.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/regeneratorAsyncGen.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/regeneratorAsyncIterator.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/regeneratorDefine.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/regeneratorKeys.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/regeneratorRuntime.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/regeneratorValues.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/setPrototypeOf.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/slicedToArray.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/toPrimitive.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/toPropertyKey.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/typeof.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/unsupportedIterableToArray.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/wrapNativeSuper.js", "(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js", "(app-pages-browser)/./node_modules/@tanstack/query-core/build/modern/focusManager.js", "(app-pages-browser)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js", "(app-pages-browser)/./node_modules/@tanstack/query-core/build/modern/mutation.js", "(app-pages-browser)/./node_modules/@tanstack/query-core/build/modern/mutationCache.js", "(app-pages-browser)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js", "(app-pages-browser)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js", "(app-pages-browser)/./node_modules/@tanstack/query-core/build/modern/query.js", "(app-pages-browser)/./node_modules/@tanstack/query-core/build/modern/queryCache.js", "(app-pages-browser)/./node_modules/@tanstack/query-core/build/modern/queryClient.js", "(app-pages-browser)/./node_modules/@tanstack/query-core/build/modern/removable.js", "(app-pages-browser)/./node_modules/@tanstack/query-core/build/modern/retryer.js", "(app-pages-browser)/./node_modules/@tanstack/query-core/build/modern/subscribable.js", "(app-pages-browser)/./node_modules/@tanstack/query-core/build/modern/thenable.js", "(app-pages-browser)/./node_modules/@tanstack/query-core/build/modern/utils.js", "(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js", "(app-pages-browser)/./node_modules/next-auth/client/_utils.js", "(app-pages-browser)/./node_modules/next-auth/core/errors.js", "(app-pages-browser)/./node_modules/next-auth/react/index.js", "(app-pages-browser)/./node_modules/next-auth/react/types.js", "(app-pages-browser)/./node_modules/next-auth/utils/logger.js", "(app-pages-browser)/./node_modules/next-auth/utils/parse-url.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5Cinno-crm%5C%5Cinno-crm-staff%5C%5Ccomponents%5C%5Cproviders%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5Cinno-crm%5C%5Cinno-crm-staff%5C%5Ccomponents%5C%5Cproviders%5C%5Cquery-provider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5Cinno-crm%5C%5Cinno-crm-staff%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5Cinno-crm%5C%5Cinno-crm-staff%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js", "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js", "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js", "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}", "(app-pages-browser)/./components/forms/lead-form.tsx", "(app-pages-browser)/./components/ui/button.tsx", "(app-pages-browser)/./components/ui/input.tsx", "(app-pages-browser)/./components/ui/label.tsx", "(app-pages-browser)/./contexts/branch-context.tsx", "(app-pages-browser)/./lib/utils.ts", "(app-pages-browser)/./node_modules/@hookform/resolvers/dist/resolvers.mjs", "(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-label/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-primitive/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-slot/dist/index.mjs", "(app-pages-browser)/./node_modules/class-variance-authority/dist/index.mjs", "(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5Cinno-crm%5C%5Cinno-crm-staff%5C%5Ccomponents%5C%5Cforms%5C%5Clead-form.tsx%22%2C%22ids%22%3A%5B%22LeadForm%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5Cinno-crm%5C%5Cinno-crm-staff%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=false!", "(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js", "(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js", "(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs", "(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs", "(app-pages-browser)/./node_modules/zod/dist/esm/index.js", "(app-pages-browser)/./node_modules/zod/dist/esm/v3/ZodError.js", "(app-pages-browser)/./node_modules/zod/dist/esm/v3/errors.js", "(app-pages-browser)/./node_modules/zod/dist/esm/v3/external.js", "(app-pages-browser)/./node_modules/zod/dist/esm/v3/helpers/errorUtil.js", "(app-pages-browser)/./node_modules/zod/dist/esm/v3/helpers/parseUtil.js", "(app-pages-browser)/./node_modules/zod/dist/esm/v3/helpers/typeAliases.js", "(app-pages-browser)/./node_modules/zod/dist/esm/v3/helpers/util.js", "(app-pages-browser)/./node_modules/zod/dist/esm/v3/index.js", "(app-pages-browser)/./node_modules/zod/dist/esm/v3/locales/en.js", "(app-pages-browser)/./node_modules/zod/dist/esm/v3/types.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=false!", "(app-pages-browser)/./app/auth/signin/page.tsx", "(app-pages-browser)/./components/ui/alert.tsx", "(app-pages-browser)/./components/ui/card.tsx", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js", "(app-pages-browser)/./node_modules/next/dist/api/navigation.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5Cinno-crm%5C%5Cinno-crm-staff%5C%5Capp%5C%5Cauth%5C%5Csignin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./components/dashboard/header.tsx", "(app-pages-browser)/./components/dashboard/sidebar.tsx", "(app-pages-browser)/./components/notifications/notification-dropdown.tsx", "(app-pages-browser)/./components/ui/badge.tsx", "(app-pages-browser)/./components/ui/branch-switcher.tsx", "(app-pages-browser)/./components/ui/dropdown-menu.tsx", "(app-pages-browser)/./components/ui/scroll-area.tsx", "(app-pages-browser)/./components/ui/toast.tsx", "(app-pages-browser)/./components/ui/toaster.tsx", "(app-pages-browser)/./hooks/use-toast.ts", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/esm/typeof.js", "(app-pages-browser)/./node_modules/@floating-ui/core/dist/floating-ui.core.mjs", "(app-pages-browser)/./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs", "(app-pages-browser)/./node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs", "(app-pages-browser)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs", "(app-pages-browser)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs", "(app-pages-browser)/./node_modules/@radix-ui/number/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/primitive/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-arrow/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-collection/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-context/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-direction/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-id/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-menu/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-popper/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-portal/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-presence/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-roving-focus/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-toast/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-size/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs", "(app-pages-browser)/./node_modules/@swc/helpers/esm/_check_private_redeclaration.js", "(app-pages-browser)/./node_modules/@swc/helpers/esm/_class_apply_descriptor_get.js", "(app-pages-browser)/./node_modules/@swc/helpers/esm/_class_apply_descriptor_set.js", "(app-pages-browser)/./node_modules/@swc/helpers/esm/_class_extract_field_descriptor.js", "(app-pages-browser)/./node_modules/@swc/helpers/esm/_class_private_field_get.js", "(app-pages-browser)/./node_modules/@swc/helpers/esm/_class_private_field_init.js", "(app-pages-browser)/./node_modules/@swc/helpers/esm/_class_private_field_set.js", "(app-pages-browser)/./node_modules/aria-hidden/dist/es2015/index.js", "(app-pages-browser)/./node_modules/date-fns/esm/_lib/assign/index.js", "(app-pages-browser)/./node_modules/date-fns/esm/_lib/cloneObject/index.js", "(app-pages-browser)/./node_modules/date-fns/esm/_lib/defaultLocale/index.js", "(app-pages-browser)/./node_modules/date-fns/esm/_lib/defaultOptions/index.js", "(app-pages-browser)/./node_modules/date-fns/esm/_lib/getTimezoneOffsetInMilliseconds/index.js", "(app-pages-browser)/./node_modules/date-fns/esm/_lib/requiredArgs/index.js", "(app-pages-browser)/./node_modules/date-fns/esm/_lib/roundingMethods/index.js", "(app-pages-browser)/./node_modules/date-fns/esm/compareAsc/index.js", "(app-pages-browser)/./node_modules/date-fns/esm/differenceInCalendarMonths/index.js", "(app-pages-browser)/./node_modules/date-fns/esm/differenceInMilliseconds/index.js", "(app-pages-browser)/./node_modules/date-fns/esm/differenceInMonths/index.js", "(app-pages-browser)/./node_modules/date-fns/esm/differenceInSeconds/index.js", "(app-pages-browser)/./node_modules/date-fns/esm/endOfDay/index.js", "(app-pages-browser)/./node_modules/date-fns/esm/endOfMonth/index.js", "(app-pages-browser)/./node_modules/date-fns/esm/formatDistance/index.js", "(app-pages-browser)/./node_modules/date-fns/esm/formatDistanceToNow/index.js", "(app-pages-browser)/./node_modules/date-fns/esm/isLastDayOfMonth/index.js", "(app-pages-browser)/./node_modules/date-fns/esm/locale/_lib/buildFormatLongFn/index.js", "(app-pages-browser)/./node_modules/date-fns/esm/locale/_lib/buildLocalizeFn/index.js", "(app-pages-browser)/./node_modules/date-fns/esm/locale/_lib/buildMatchFn/index.js", "(app-pages-browser)/./node_modules/date-fns/esm/locale/_lib/buildMatchPatternFn/index.js", "(app-pages-browser)/./node_modules/date-fns/esm/locale/en-US/_lib/formatDistance/index.js", "(app-pages-browser)/./node_modules/date-fns/esm/locale/en-US/_lib/formatLong/index.js", "(app-pages-browser)/./node_modules/date-fns/esm/locale/en-US/_lib/formatRelative/index.js", "(app-pages-browser)/./node_modules/date-fns/esm/locale/en-US/_lib/localize/index.js", "(app-pages-browser)/./node_modules/date-fns/esm/locale/en-US/_lib/match/index.js", "(app-pages-browser)/./node_modules/date-fns/esm/locale/en-US/index.js", "(app-pages-browser)/./node_modules/date-fns/esm/toDate/index.js", "(app-pages-browser)/./node_modules/get-nonce/dist/es2015/index.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-check.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pie-chart.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5Cinno-crm%5C%5Cinno-crm-staff%5C%5Ccomponents%5C%5Cdashboard%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5Cinno-crm%5C%5Cinno-crm-staff%5C%5Ccomponents%5C%5Cdashboard%5C%5Csidebar.tsx%22%2C%22ids%22%3A%5B%22Sidebar%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5Cinno-crm%5C%5Cinno-crm-staff%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5Cinno-crm%5C%5Cinno-crm-staff%5C%5Ccontexts%5C%5Cbranch-context.tsx%22%2C%22ids%22%3A%5B%22BranchProvider%22%5D%7D&server=false!", "(app-pages-browser)/./node_modules/react-remove-scroll-bar/dist/es2015/component.js", "(app-pages-browser)/./node_modules/react-remove-scroll-bar/dist/es2015/constants.js", "(app-pages-browser)/./node_modules/react-remove-scroll-bar/dist/es2015/index.js", "(app-pages-browser)/./node_modules/react-remove-scroll-bar/dist/es2015/utils.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/Combination.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/SideEffect.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/UI.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/handleScroll.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/medium.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/sidecar.js", "(app-pages-browser)/./node_modules/react-style-singleton/dist/es2015/component.js", "(app-pages-browser)/./node_modules/react-style-singleton/dist/es2015/hook.js", "(app-pages-browser)/./node_modules/react-style-singleton/dist/es2015/index.js", "(app-pages-browser)/./node_modules/react-style-singleton/dist/es2015/singleton.js", "(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs", "(app-pages-browser)/./node_modules/use-callback-ref/dist/es2015/assignRef.js", "(app-pages-browser)/./node_modules/use-callback-ref/dist/es2015/useMergeRef.js", "(app-pages-browser)/./node_modules/use-callback-ref/dist/es2015/useRef.js", "(app-pages-browser)/./node_modules/use-sidecar/dist/es2015/exports.js", "(app-pages-browser)/./node_modules/use-sidecar/dist/es2015/medium.js", "(app-pages-browser)/./app/(dashboard)/dashboard/leads/page.tsx", "(app-pages-browser)/./components/leads/call-manager.tsx", "(app-pages-browser)/./components/leads/date-filter.tsx", "(app-pages-browser)/./components/leads/group-assignment-modal.tsx", "(app-pages-browser)/./components/leads/leads-list.tsx", "(app-pages-browser)/./components/ui/dialog.tsx", "(app-pages-browser)/./components/ui/select.tsx", "(app-pages-browser)/./components/ui/table.tsx", "(app-pages-browser)/./components/ui/tabs.tsx", "(app-pages-browser)/./components/ui/textarea.tsx", "(app-pages-browser)/./node_modules/@radix-ui/react-dialog/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-select/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-tabs/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/archive.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone-off.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5Cinno-crm%5C%5Cinno-crm-staff%5C%5Capp%5C%5C(dashboard)%5C%5Cdashboard%5C%5Cleads%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./app/(dashboard)/dashboard/page.tsx", "(app-pages-browser)/./components/dashboard/activity-feed.tsx", "(app-pages-browser)/./lib/activity-utils.ts", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5Cinno-crm%5C%5Cinno-crm-staff%5C%5Capp%5C%5C(dashboard)%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"]}
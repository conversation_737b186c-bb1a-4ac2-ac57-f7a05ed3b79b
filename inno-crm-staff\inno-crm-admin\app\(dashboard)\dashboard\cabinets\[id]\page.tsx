'use client'

import { useState, useEffect, useCallback } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  ArrowLeft, Building, Users, MapPin, Package, Calendar, Clock,
  Plus, Edit, Trash2, Loader2, User, BookOpen
} from 'lucide-react'
import Link from 'next/link'
import CabinetScheduleForm from '@/components/forms/cabinet-schedule-form'

interface CabinetDetail {
  id: string
  name: string
  number: string
  capacity: number
  floor?: number
  building?: string
  branch: string
  equipment?: string
  notes?: string
  isActive: boolean
  createdAt: string
  groups: Array<{
    id: string
    name: string
    course: {
      name: string
      level: string
    }
    teacher: {
      user: {
        name: string
      }
    }
    _count: {
      enrollments: number
    }
  }>
  schedules: Array<{
    id: string
    dayOfWeek: number
    startTime: string
    endTime: string
    title?: string
    isBlocked: boolean
    group?: {
      id: string
      name: string
      course: {
        name: string
        level: string
      }
      teacher: {
        user: {
          name: string
        }
      }
    }
  }>
  _count: {
    groups: number
    schedules: number
  }
}

const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']

export default function CabinetDetailPage() {
  const params = useParams()
  const router = useRouter()
  const cabinetId = params.id as string

  const [cabinet, setCabinet] = useState<CabinetDetail | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isScheduleDialogOpen, setIsScheduleDialogOpen] = useState(false)

  const fetchCabinet = useCallback(async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/cabinets/${cabinetId}`)

      if (!response.ok) {
        if (response.status === 404) {
          router.push('/dashboard/cabinets')
          return
        }
        throw new Error('Failed to fetch cabinet details')
      }

      const data = await response.json()
      setCabinet(data)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }, [cabinetId, router])

  useEffect(() => {
    if (cabinetId) {
      fetchCabinet()
    }
  }, [cabinetId, fetchCabinet])

  const handleCreateSchedule = async (data: any) => {
    const response = await fetch(`/api/cabinets/${cabinetId}/schedules`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to create schedule')
    }

    setIsScheduleDialogOpen(false)
    fetchCabinet()
  }

  // Group schedules by day
  const schedulesByDay = cabinet?.schedules.reduce((acc, schedule) => {
    const day = schedule.dayOfWeek
    if (!acc[day]) acc[day] = []
    acc[day].push(schedule)
    return acc
  }, {} as Record<number, typeof cabinet.schedules>) || {}

  // Sort schedules within each day by start time
  Object.keys(schedulesByDay).forEach(day => {
    schedulesByDay[parseInt(day)].sort((a, b) => a.startTime.localeCompare(b.startTime))
  })

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (error || !cabinet) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertDescription>{error || 'Cabinet not found'}</AlertDescription>
        </Alert>
        <Link href="/dashboard/cabinets">
          <Button variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Cabinets
          </Button>
        </Link>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/cabinets">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{cabinet.name}</h1>
            <p className="text-gray-600">Cabinet #{cabinet.number}</p>
          </div>
        </div>
        <Badge variant={cabinet.isActive ? 'default' : 'secondary'}>
          {cabinet.isActive ? 'Active' : 'Inactive'}
        </Badge>
      </div>

      {/* Cabinet Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Capacity</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{cabinet.capacity}</div>
            <p className="text-xs text-muted-foreground">students</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Groups</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{cabinet._count.groups}</div>
            <p className="text-xs text-muted-foreground">assigned groups</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Schedules</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{cabinet._count.schedules}</div>
            <p className="text-xs text-muted-foreground">time slots</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Location</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-sm">
              {cabinet.building && <div>{cabinet.building}</div>}
              {cabinet.floor !== undefined && <div>Floor {cabinet.floor}</div>}
              <div className="text-xs text-muted-foreground">{cabinet.branch}</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Cabinet Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Equipment and Notes */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Package className="h-5 w-5 mr-2" />
              Equipment & Notes
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {cabinet.equipment && (
              <div>
                <h4 className="font-medium text-sm text-gray-700 mb-2">Equipment</h4>
                <p className="text-sm text-gray-600 whitespace-pre-wrap">{cabinet.equipment}</p>
              </div>
            )}
            {cabinet.notes && (
              <div>
                <h4 className="font-medium text-sm text-gray-700 mb-2">Notes</h4>
                <p className="text-sm text-gray-600 whitespace-pre-wrap">{cabinet.notes}</p>
              </div>
            )}
            {!cabinet.equipment && !cabinet.notes && (
              <p className="text-sm text-gray-500">No additional information available.</p>
            )}
          </CardContent>
        </Card>

        {/* Assigned Groups */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BookOpen className="h-5 w-5 mr-2" />
              Assigned Groups
            </CardTitle>
          </CardHeader>
          <CardContent>
            {cabinet.groups.length > 0 ? (
              <div className="space-y-3">
                {cabinet.groups.map((group) => (
                  <div key={group.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium">{group.name}</div>
                      <div className="text-sm text-gray-600">
                        {group.course.name} ({group.course.level})
                      </div>
                      <div className="text-xs text-gray-500">
                        Teacher: {group.teacher.user.name}
                      </div>
                    </div>
                    <Badge variant="outline">
                      {group._count.enrollments} students
                    </Badge>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">No groups assigned to this cabinet.</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Weekly Schedule */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Weekly Schedule
            </div>
            <Dialog open={isScheduleDialogOpen} onOpenChange={setIsScheduleDialogOpen}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Schedule
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Add Schedule</DialogTitle>
                  <DialogDescription>
                    Create a new schedule entry for this cabinet.
                  </DialogDescription>
                </DialogHeader>
                <CabinetScheduleForm
                  cabinetId={cabinetId}
                  onSubmit={handleCreateSchedule}
                  onCancel={() => setIsScheduleDialogOpen(false)}
                  isEditing={false}
                />
              </DialogContent>
            </Dialog>
          </CardTitle>
          <CardDescription>
            Cabinet usage schedule throughout the week
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[0, 1, 2, 3, 4, 5, 6].map((dayIndex) => (
              <div key={dayIndex} className="border rounded-lg p-4">
                <h4 className="font-medium text-sm mb-3">{dayNames[dayIndex]}</h4>
                <div className="space-y-2">
                  {schedulesByDay[dayIndex]?.map((schedule) => (
                    <div
                      key={schedule.id}
                      className={`p-2 rounded text-xs ${
                        schedule.isBlocked
                          ? 'bg-red-100 text-red-800 border border-red-200'
                          : 'bg-blue-100 text-blue-800 border border-blue-200'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <span className="font-medium">
                          {schedule.startTime} - {schedule.endTime}
                        </span>
                      </div>
                      {schedule.group ? (
                        <div className="mt-1">
                          <div className="font-medium">{schedule.group.name}</div>
                          <div className="text-xs opacity-75">
                            {schedule.group.course.name} - {schedule.group.teacher.user.name}
                          </div>
                        </div>
                      ) : (
                        <div className="mt-1">
                          {schedule.title || 'Blocked Time'}
                        </div>
                      )}
                    </div>
                  )) || (
                    <p className="text-xs text-gray-500">No schedules</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

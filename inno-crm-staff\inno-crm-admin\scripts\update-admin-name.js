const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updateAdminName() {
  try {
    console.log('Updating admin name...');
    
    const result = await prisma.user.update({
      where: { phone: '+998906006299' },
      data: { name: '<PERSON><PERSON><PERSON><PERSON>' }
    });
    
    console.log('✅ Updated admin name:', result.name);
    
    // Also check current data counts
    const groupCount = await prisma.group.count();
    const studentCount = await prisma.student.count();
    const leadCount = await prisma.lead.count();
    
    console.log('\n📊 Current Data Counts:');
    console.log(`Groups: ${groupCount}`);
    console.log(`Students: ${studentCount}`);
    console.log(`Leads: ${leadCount}`);
    
    // Check branch distribution
    const groupsByBranch = await prisma.group.groupBy({
      by: ['branch'],
      _count: { branch: true }
    });
    
    const studentsByBranch = await prisma.student.groupBy({
      by: ['branch'],
      _count: { branch: true }
    });
    
    const leadsByBranch = await prisma.lead.groupBy({
      by: ['branch'],
      _count: { branch: true }
    });
    
    console.log('\n🏢 Data by Branch:');
    console.log('Groups:', groupsByBranch);
    console.log('Students:', studentsByBranch);
    console.log('Leads:', leadsByBranch);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateAdminName();

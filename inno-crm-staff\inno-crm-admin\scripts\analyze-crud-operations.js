const fs = require('fs')
const path = require('path')

console.log('🔍 CRUD Operations Analysis for inno-crm')
console.log('=' .repeat(50))

// Define expected API endpoints and their CRUD operations
const expectedEndpoints = {
  'students': { create: true, read: true, update: true, delete: true },
  'teachers': { create: true, read: true, update: true, delete: true },
  'courses': { create: true, read: true, update: true, delete: true },
  'groups': { create: true, read: true, update: true, delete: true },
  'enrollments': { create: true, read: true, update: true, delete: true },
  'payments': { create: true, read: true, update: true, delete: true },
  'attendance': { create: true, read: true, update: true, delete: true },
  'assessments': { create: true, read: true, update: true, delete: true },
  'leads': { create: true, read: true, update: true, delete: true },
  'users': { create: true, read: true, update: true, delete: true },
  'classes': { create: true, read: true, update: true, delete: true },
  'notifications': { create: true, read: true, update: false, delete: false },
  'workflows': { create: false, read: true, update: false, delete: false },
  'analytics': { create: false, read: true, update: false, delete: false },
  'kpis': { create: false, read: true, update: false, delete: false },
  'reports': { create: false, read: true, update: false, delete: false },
  'activity-logs': { create: true, read: true, update: false, delete: false }
}

// Check API routes
const apiDir = 'app/api'
const missingRoutes = []
const incompleteRoutes = []
const workingRoutes = []

console.log('\n📁 Checking API Routes...')
console.log('-'.repeat(30))

for (const [endpoint, expectedOps] of Object.entries(expectedEndpoints)) {
  const routePath = path.join(apiDir, endpoint, 'route.ts')
  const idRoutePath = path.join(apiDir, endpoint, '[id]', 'route.ts')
  
  let hasMainRoute = fs.existsSync(routePath)
  let hasIdRoute = fs.existsSync(idRoutePath)
  
  console.log(`\n🔍 ${endpoint.toUpperCase()}:`)
  
  if (!hasMainRoute && !hasIdRoute) {
    console.log(`  ❌ MISSING: No API routes found`)
    missingRoutes.push({
      endpoint,
      missing: ['main route', 'id route'],
      expectedOps
    })
    continue
  }
  
  // Check main route operations
  let mainRouteOps = { GET: false, POST: false }
  if (hasMainRoute) {
    try {
      const content = fs.readFileSync(routePath, 'utf8')
      mainRouteOps.GET = /export\s+async\s+function\s+GET/.test(content)
      mainRouteOps.POST = /export\s+async\s+function\s+POST/.test(content)
      
      console.log(`  ✅ Main route exists`)
      console.log(`    - GET (Read): ${mainRouteOps.GET ? '✅' : '❌'}`)
      console.log(`    - POST (Create): ${mainRouteOps.POST ? '✅' : '❌'}`)
    } catch (error) {
      console.log(`  ❌ Error reading main route: ${error.message}`)
    }
  } else {
    console.log(`  ❌ Main route missing`)
  }
  
  // Check ID route operations
  let idRouteOps = { GET: false, PUT: false, DELETE: false }
  if (hasIdRoute) {
    try {
      const content = fs.readFileSync(idRoutePath, 'utf8')
      idRouteOps.GET = /export\s+async\s+function\s+GET/.test(content)
      idRouteOps.PUT = /export\s+async\s+function\s+PUT/.test(content)
      idRouteOps.DELETE = /export\s+async\s+function\s+DELETE/.test(content)
      
      console.log(`  ✅ ID route exists`)
      console.log(`    - GET (Read by ID): ${idRouteOps.GET ? '✅' : '❌'}`)
      console.log(`    - PUT (Update): ${idRouteOps.PUT ? '✅' : '❌'}`)
      console.log(`    - DELETE (Delete): ${idRouteOps.DELETE ? '✅' : '❌'}`)
    } catch (error) {
      console.log(`  ❌ Error reading ID route: ${error.message}`)
    }
  } else {
    console.log(`  ❌ ID route missing`)
  }
  
  // Analyze completeness
  const issues = []
  
  if (expectedOps.create && !mainRouteOps.POST) {
    issues.push('CREATE (POST) missing')
  }
  
  if (expectedOps.read && !mainRouteOps.GET && !idRouteOps.GET) {
    issues.push('READ (GET) missing')
  }
  
  if (expectedOps.update && !idRouteOps.PUT) {
    issues.push('UPDATE (PUT) missing')
  }
  
  if (expectedOps.delete && !idRouteOps.DELETE) {
    issues.push('DELETE missing')
  }
  
  if (issues.length > 0) {
    console.log(`  ⚠️  Issues: ${issues.join(', ')}`)
    incompleteRoutes.push({
      endpoint,
      issues,
      hasMainRoute,
      hasIdRoute,
      mainRouteOps,
      idRouteOps,
      expectedOps
    })
  } else {
    console.log(`  ✅ All required operations present`)
    workingRoutes.push(endpoint)
  }
}

console.log('\n' + '='.repeat(50))
console.log('📊 SUMMARY')
console.log('='.repeat(50))

console.log(`\n✅ Working endpoints (${workingRoutes.length}):`)
workingRoutes.forEach(endpoint => {
  console.log(`  - ${endpoint}`)
})

console.log(`\n⚠️  Incomplete endpoints (${incompleteRoutes.length}):`)
incompleteRoutes.forEach(({ endpoint, issues }) => {
  console.log(`  - ${endpoint}: ${issues.join(', ')}`)
})

console.log(`\n❌ Missing endpoints (${missingRoutes.length}):`)
missingRoutes.forEach(({ endpoint }) => {
  console.log(`  - ${endpoint}: No API routes found`)
})

console.log('\n' + '='.repeat(50))
console.log('🔧 RECOMMENDED ACTIONS')
console.log('='.repeat(50))

if (missingRoutes.length > 0) {
  console.log('\n1. CREATE MISSING API ROUTES:')
  missingRoutes.forEach(({ endpoint, expectedOps }) => {
    console.log(`   - Create app/api/${endpoint}/route.ts`)
    if (expectedOps.update || expectedOps.delete) {
      console.log(`   - Create app/api/${endpoint}/[id]/route.ts`)
    }
  })
}

if (incompleteRoutes.length > 0) {
  console.log('\n2. FIX INCOMPLETE API ROUTES:')
  incompleteRoutes.forEach(({ endpoint, issues, hasMainRoute, hasIdRoute }) => {
    console.log(`   - ${endpoint}:`)
    issues.forEach(issue => {
      console.log(`     * ${issue}`)
    })
  })
}

console.log('\n3. VERIFY FRONTEND INTEGRATION:')
console.log('   - Check dashboard pages use correct API endpoints')
console.log('   - Verify form submissions work properly')
console.log('   - Test error handling and validation')

console.log('\n4. RUN TESTS:')
console.log('   - Create unit tests for each CRUD operation')
console.log('   - Test with different user roles and permissions')
console.log('   - Verify data integrity and relationships')

// Export results for further processing
const results = {
  workingRoutes,
  incompleteRoutes,
  missingRoutes,
  totalEndpoints: Object.keys(expectedEndpoints).length
}

fs.writeFileSync('crud-analysis-results.json', JSON.stringify(results, null, 2))
console.log('\n📄 Results saved to crud-analysis-results.json')

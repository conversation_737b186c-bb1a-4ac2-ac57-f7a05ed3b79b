'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useToast } from '@/hooks/use-toast'

export default function TestNotificationsPage() {
  const { toast } = useToast()

  const showSuccessToast = () => {
    toast({
      title: "Success!",
      description: "This is a success notification.",
    })
  }

  const showErrorToast = () => {
    toast({
      variant: "destructive",
      title: "Error!",
      description: "This is an error notification.",
    })
  }

  const showInfoToast = () => {
    toast({
      title: "Information",
      description: "This is an informational notification.",
    })
  }

  const showWarningToast = () => {
    toast({
      title: "Warning",
      description: "This is a warning notification.",
    })
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Test Notifications</h1>
        <p className="text-gray-600">Test the notification system</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Toast Notifications</CardTitle>
          <CardDescription>Test different types of toast notifications</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button onClick={showSuccessToast} variant="default">
              Success Toast
            </Button>
            <Button onClick={showErrorToast} variant="destructive">
              Error Toast
            </Button>
            <Button onClick={showInfoToast} variant="outline">
              Info Toast
            </Button>
            <Button onClick={showWarningToast} variant="secondary">
              Warning Toast
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

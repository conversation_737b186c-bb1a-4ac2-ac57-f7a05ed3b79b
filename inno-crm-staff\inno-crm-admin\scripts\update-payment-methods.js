const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function updatePaymentMethods() {
  try {
    console.log('Starting payment method migration...')

    // Step 1: Add CARD to the enum
    console.log('Step 1: Adding CARD to PaymentMethod enum...')
    await prisma.$executeRaw`
      ALTER TYPE "PaymentMethod" ADD VALUE 'CARD'
    `

    // Step 2: Check current payment methods
    const currentMethods = await prisma.$queryRaw`
      SELECT method, COUNT(*) as count
      FROM payments
      GROUP BY method
    `

    console.log('Current payment method distribution:')
    currentMethods.forEach(method => {
      console.log(`${method.method}: ${method.count} records`)
    })

    // Step 3: Update all non-CASH methods to CARD
    console.log('Step 2: Updating payment methods to CARD...')
    const updateResult = await prisma.$executeRaw`
      UPDATE payments
      SET method = 'CARD'
      WHERE method IN ('UZCARD', 'HUMO', 'PAYME', 'CLICK', 'BANK_TRANSFER')
    `

    console.log(`Updated ${updateResult} payment records to use CARD method`)

    // Step 4: Check updated payment methods
    const updatedMethods = await prisma.$queryRaw`
      SELECT method, COUNT(*) as count
      FROM payments
      GROUP BY method
    `

    console.log('Updated payment method distribution:')
    updatedMethods.forEach(method => {
      console.log(`${method.method}: ${method.count} records`)
    })

    console.log('Migration completed! You can now run: npx prisma db push')

  } catch (error) {
    console.error('Error updating payment methods:', error)
  } finally {
    await prisma.$disconnect()
  }
}

updatePaymentMethods()

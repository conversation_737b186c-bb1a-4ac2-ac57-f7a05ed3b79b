'use client'

import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Label } from '@/components/ui/label'
import { formatDate } from '@/lib/utils'
import { Search, Plus, FileText, CheckCircle, XCircle, Users, Award, GraduationCap } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface Assessment {
  id: string
  studentId?: string
  groupId?: string
  testName: string
  type: string
  level?: string
  score?: number
  maxScore?: number
  passed: boolean
  completedAt?: string
  createdAt: string
  student?: {
    id: string
    user: {
      id: string
      name: string
      email?: string
    }
  }
  group?: {
    id: string
    name: string
    course: {
      name: string
      level: string
    }
  }
}

interface Group {
  id: string
  name: string
  course: {
    name: string
    level: string
  }
  enrollments: {
    student: {
      id: string
      user: {
        id: string
        name: string
      }
    }
  }[]
}

export default function AssessmentsPage() {
  const { data: session } = useSession()
  const [assessments, setAssessments] = useState<Assessment[]>([])
  const [groups, setGroups] = useState<Group[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState<string>('all')
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null)
  const [testName, setTestName] = useState('')
  const [testType, setTestType] = useState('')
  const [studentScores, setStudentScores] = useState<{[key: string]: {score: number, maxScore: number, passed: boolean}}>({})
  const { toast } = useToast()

  // Check if user has Test Administrator role
  const userRole = session?.user?.role as string
  const isTestAdministrator = ['ADMIN', 'MANAGER', 'TEACHER'].includes(userRole)

  const fetchAssessments = useCallback(async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams()
      if (selectedType && selectedType !== 'all') params.append('type', selectedType)

      const response = await fetch(`/api/assessments?${params}`)
      if (!response.ok) throw new Error('Failed to fetch assessments')

      const data = await response.json()
      setAssessments(data.assessments || [])
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to fetch assessments",
      })
    } finally {
      setLoading(false)
    }
  }, [selectedType, toast])

  const fetchGroups = async () => {
    try {
      const response = await fetch('/api/groups')
      if (!response.ok) throw new Error('Failed to fetch groups')

      const data = await response.json()
      setGroups(data.groups || [])
    } catch (error) {
      console.error('Error fetching groups:', error)
      // Set empty array if groups API fails
      setGroups([])
    }
  }

  useEffect(() => {
    if (isTestAdministrator) {
      fetchAssessments()
      fetchGroups()
    }
  }, [fetchAssessments, isTestAdministrator])

  const filteredAssessments = assessments.filter(assessment => {
    const studentName = assessment.student?.user.name || ''
    const groupName = assessment.group?.name || ''
    const testName = assessment.testName || ''

    return studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
           groupName.toLowerCase().includes(searchTerm.toLowerCase()) ||
           testName.toLowerCase().includes(searchTerm.toLowerCase()) ||
           assessment.type.toLowerCase().includes(searchTerm.toLowerCase())
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!selectedGroup || !testName || !testType) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please select a group, enter test name, and select test type",
      })
      return
    }

    try {
      // Create assessments for each student in the group
      const assessmentPromises = selectedGroup.enrollments.map(enrollment => {
        const studentId = enrollment.student.id
        const studentScore = studentScores[studentId]

        if (!studentScore) return null

        return fetch('/api/assessments', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            studentId,
            groupId: selectedGroup.id,
            testName,
            type: testType,
            level: selectedGroup.course.level,
            score: studentScore.score,
            maxScore: studentScore.maxScore,
            passed: studentScore.passed,
            completedAt: new Date().toISOString(),
          }),
        })
      }).filter(Boolean)

      await Promise.all(assessmentPromises)

      toast({
        title: "Success",
        description: `Assessment results recorded for ${assessmentPromises.length} students`,
      })

      setIsCreateDialogOpen(false)
      resetForm()
      fetchAssessments()
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to record assessment results",
      })
    }
  }

  const resetForm = () => {
    setSelectedGroup(null)
    setTestName('')
    setTestType('')
    setStudentScores({})
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'LEVEL_TEST': return 'bg-green-100 text-green-800'
      case 'PROGRESS_TEST': return 'bg-yellow-100 text-yellow-800'
      case 'FINAL_EXAM': return 'bg-red-100 text-red-800'
      case 'GROUP_TEST': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPassedIcon = (passed: boolean) => {
    return passed ? (
      <CheckCircle className="h-5 w-5 text-green-600" />
    ) : (
      <XCircle className="h-5 w-5 text-red-600" />
    )
  }

  const updateStudentScore = (studentId: string, field: string, value: any) => {
    setStudentScores(prev => ({
      ...prev,
      [studentId]: {
        ...prev[studentId],
        [field]: value
      }
    }))
  }

  // Show unauthorized message for non-Test Administrator roles
  if (!isTestAdministrator) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-600">Only Test Administrators can access the Assessment system.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Assessment Recording</h1>
          <p className="text-gray-600">Record post-test results for student groups</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="h-4 w-4 mr-2" />
              Record Test Results
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Record Test Results</DialogTitle>
              <DialogDescription>
                Select a group and enter test results for all students.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Step 1: Select Group */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Users className="h-5 w-5 text-blue-600" />
                  <h3 className="text-lg font-medium">Step 1: Select Student Group</h3>
                </div>
                <Select value={selectedGroup?.id || ''} onValueChange={(value) => {
                  const group = groups.find(g => g.id === value)
                  setSelectedGroup(group || null)
                  setStudentScores({}) // Reset scores when group changes
                }}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a student group" />
                  </SelectTrigger>
                  <SelectContent>
                    {groups.map((group) => (
                      <SelectItem key={group.id} value={group.id}>
                        {group.name} - {group.course.name} ({group.course.level})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Step 2: Test Details */}
              {selectedGroup && (
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <FileText className="h-5 w-5 text-green-600" />
                    <h3 className="text-lg font-medium">Step 2: Test Information</h3>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="testName">Test Name *</Label>
                      <Input
                        id="testName"
                        value={testName}
                        onChange={(e) => setTestName(e.target.value)}
                        placeholder="e.g., Unit 5 Test, Mid-term Exam"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="testType">Test Type *</Label>
                      <Select value={testType} onValueChange={setTestType}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select test type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="LEVEL_TEST">Level Test</SelectItem>
                          <SelectItem value="PROGRESS_TEST">Progress Test</SelectItem>
                          <SelectItem value="FINAL_EXAM">Final Exam</SelectItem>
                          <SelectItem value="GROUP_TEST">Group Test</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              )}

              {/* Step 3: Student Scores */}
              {selectedGroup && testName && testType && (
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <GraduationCap className="h-5 w-5 text-purple-600" />
                    <h3 className="text-lg font-medium">Step 3: Enter Individual Scores</h3>
                  </div>
                  <div className="border rounded-lg p-4 max-h-60 overflow-y-auto">
                    <div className="space-y-3">
                      {selectedGroup.enrollments.map((enrollment) => {
                        const studentId = enrollment.student.id
                        const studentScore = studentScores[studentId] || { score: 0, maxScore: 100, passed: false }

                        return (
                          <div key={studentId} className="grid grid-cols-5 gap-3 items-center p-3 border rounded bg-gray-50">
                            <div className="col-span-2">
                              <span className="font-medium">{enrollment.student.user.name}</span>
                            </div>
                            <div>
                              <Label htmlFor={`score-${studentId}`} className="text-xs">Score</Label>
                              <Input
                                id={`score-${studentId}`}
                                type="number"
                                min="0"
                                value={studentScore.score}
                                onChange={(e) => {
                                  const score = parseInt(e.target.value) || 0
                                  const passed = score >= (studentScore.maxScore * 0.6) // 60% pass rate
                                  updateStudentScore(studentId, 'score', score)
                                  updateStudentScore(studentId, 'passed', passed)
                                }}
                                className="h-8"
                              />
                            </div>
                            <div>
                              <Label htmlFor={`maxScore-${studentId}`} className="text-xs">Max Score</Label>
                              <Input
                                id={`maxScore-${studentId}`}
                                type="number"
                                min="1"
                                value={studentScore.maxScore}
                                onChange={(e) => {
                                  const maxScore = parseInt(e.target.value) || 100
                                  const passed = studentScore.score >= (maxScore * 0.6)
                                  updateStudentScore(studentId, 'maxScore', maxScore)
                                  updateStudentScore(studentId, 'passed', passed)
                                }}
                                className="h-8"
                              />
                            </div>
                            <div className="flex items-center justify-center">
                              {studentScore.passed ? (
                                <CheckCircle className="h-5 w-5 text-green-600" />
                              ) : (
                                <XCircle className="h-5 w-5 text-red-600" />
                              )}
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                </div>
              )}

              <div className="flex justify-end space-x-2 pt-4 border-t">
                <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={!selectedGroup || !testName || !testType || Object.keys(studentScores).length === 0}
                >
                  Record Test Results
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <FileText className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Assessments</p>
                <p className="text-2xl font-bold text-gray-900">{assessments.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Passed</p>
                <p className="text-2xl font-bold text-gray-900">
                  {assessments.filter(a => a.passed).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <XCircle className="h-8 w-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Failed</p>
                <p className="text-2xl font-bold text-gray-900">
                  {assessments.filter(a => !a.passed).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Award className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pass Rate</p>
                <p className="text-2xl font-bold text-gray-900">
                  {assessments.length > 0 
                    ? Math.round((assessments.filter(a => a.passed).length / assessments.length) * 100)
                    : 0}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
          <CardDescription>Filter assessments by type and search</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search students, types..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger>
                <SelectValue placeholder="All Types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="LEVEL_TEST">Level Test</SelectItem>
                <SelectItem value="PROGRESS_TEST">Progress Test</SelectItem>
                <SelectItem value="FINAL_EXAM">Final Exam</SelectItem>
                <SelectItem value="GROUP_TEST">Group Test</SelectItem>
              </SelectContent>
            </Select>

            <Button onClick={fetchAssessments} variant="outline">
              <Search className="h-4 w-4 mr-2" />
              Search
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Assessments Table */}
      <Card>
        <CardHeader>
          <CardTitle>Assessment Records</CardTitle>
          <CardDescription>
            Showing {filteredAssessments.length} recorded test results
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Student/Group</TableHead>
                    <TableHead>Test Name</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Level</TableHead>
                    <TableHead>Score</TableHead>
                    <TableHead>Result</TableHead>
                    <TableHead>Completed</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAssessments.map((assessment) => (
                    <TableRow key={assessment.id}>
                      <TableCell>
                        <div>
                          {assessment.student ? (
                            <>
                              <div className="font-medium">{assessment.student.user.name}</div>
                              <div className="text-sm text-gray-500">{assessment.student.user.email}</div>
                            </>
                          ) : assessment.group ? (
                            <>
                              <div className="font-medium">{assessment.group.name}</div>
                              <div className="text-sm text-gray-500">{assessment.group.course.name}</div>
                            </>
                          ) : (
                            <span className="text-gray-400">-</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{assessment.testName || 'Legacy Assessment'}</div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getTypeColor(assessment.type)}>
                          {assessment.type.replace('_', ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell>{assessment.level || '-'}</TableCell>
                      <TableCell>
                        {assessment.score !== undefined && assessment.maxScore
                          ? `${assessment.score}/${assessment.maxScore}`
                          : '-'
                        }
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {getPassedIcon(assessment.passed)}
                          <span>{assessment.passed ? 'Passed' : 'Failed'}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {assessment.completedAt ? formatDate(assessment.completedAt) : '-'}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {filteredAssessments.length === 0 && !loading && (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No assessment records found.</p>
              <p className="text-sm text-gray-400 mt-1">Start by recording test results for a student group.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { formatDate } from '@/lib/utils'
import { 
  User, 
  Phone, 
  Mail, 
  MapPin, 
  Calendar, 
  GraduationCap, 
  CreditCard, 
  CheckCircle, 
  XCircle,
  Clock,
  AlertCircle,
  Edit,
  ArrowLeft
} from 'lucide-react'
import Link from 'next/link'

interface StudentDetail {
  id: string
  userId: string
  level: string
  branch: string
  emergencyContact: string | null
  photoUrl: string | null
  dateOfBirth: string | null
  address: string | null
  createdAt: string
  user: {
    id: string
    name: string
    phone: string
    email: string | null
    role: string
    createdAt: string
  }
  enrollments: Array<{
    id: string
    status: string
    startDate: string
    endDate: string | null
    group: {
      id: string
      name: string
      course: {
        name: string
        level: string
        duration: number
        price: number
      }
      teacher: {
        user: {
          name: string
        }
      }
    }
  }>
  payments: Array<{
    id: string
    amount: number
    method: string
    status: string
    description: string | null
    dueDate: string | null
    paidDate: string | null
    createdAt: string
  }>
  attendances: Array<{
    id: string
    status: string
    notes: string | null
    createdAt: string
    class: {
      date: string
      topic: string | null
      group: {
        name: string
      }
    }
  }>
}

export default function StudentDetailPage() {
  const params = useParams()
  const [student, setStudent] = useState<StudentDetail | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (params.id) {
      fetchStudent(params.id as string)
    }
  }, [params.id])

  const fetchStudent = async (id: string) => {
    try {
      const response = await fetch(`/api/students/${id}`)
      const data = await response.json()
      setStudent(data)
    } catch (error) {
      console.error('Error fetching student:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800'
      case 'COMPLETED':
        return 'bg-blue-100 text-blue-800'
      case 'DROPPED':
        return 'bg-red-100 text-red-800'
      case 'SUSPENDED':
        return 'bg-yellow-100 text-yellow-800'
      case 'PAID':
        return 'bg-green-100 text-green-800'
      case 'DEBT':
        return 'bg-red-100 text-red-800'
      case 'PRESENT':
        return 'bg-green-100 text-green-800'
      case 'ABSENT':
        return 'bg-red-100 text-red-800'
      case 'LATE':
        return 'bg-yellow-100 text-yellow-800'
      case 'EXCUSED':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getAttendanceIcon = (status: string) => {
    switch (status) {
      case 'PRESENT':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'ABSENT':
        return <XCircle className="h-4 w-4 text-red-600" />
      case 'LATE':
        return <Clock className="h-4 w-4 text-yellow-600" />
      case 'EXCUSED':
        return <AlertCircle className="h-4 w-4 text-blue-600" />
      default:
        return null
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount / 12500) // Convert UZS to USD for display
  }

  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>
  }

  if (!student) {
    return <div className="flex justify-center items-center h-64">Student not found</div>
  }

  // Calculate statistics
  const totalPayments = student.payments.reduce((sum, payment) => sum + payment.amount, 0)
  const paidPayments = student.payments
    .filter(p => p.status === 'PAID')
    .reduce((sum, payment) => sum + payment.amount, 0)
  const pendingPayments = totalPayments - paidPayments

  const totalClasses = student.attendances.length
  const presentClasses = student.attendances.filter(a => a.status === 'PRESENT').length
  const attendanceRate = totalClasses > 0 ? (presentClasses / totalClasses) * 100 : 0

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/students">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Students
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{student.user.name}</h1>
            <p className="text-gray-600">Student Profile</p>
          </div>
        </div>
        <Button>
          <Edit className="h-4 w-4 mr-2" />
          Edit Profile
        </Button>
      </div>

      {/* Student Info Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="h-5 w-5 mr-2" />
              Personal Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-3">
              <Phone className="h-4 w-4 text-gray-400" />
              <span>{student.user.phone}</span>
            </div>
            {student.user.email && (
              <div className="flex items-center space-x-3">
                <Mail className="h-4 w-4 text-gray-400" />
                <span>{student.user.email}</span>
              </div>
            )}
            {student.address && (
              <div className="flex items-center space-x-3">
                <MapPin className="h-4 w-4 text-gray-400" />
                <span>{student.address}</span>
              </div>
            )}
            {student.dateOfBirth && (
              <div className="flex items-center space-x-3">
                <Calendar className="h-4 w-4 text-gray-400" />
                <span>{formatDate(student.dateOfBirth)}</span>
              </div>
            )}
            <div className="flex items-center space-x-3">
              <GraduationCap className="h-4 w-4 text-gray-400" />
              <Badge>{student.level}</Badge>
            </div>
            <div className="pt-2 border-t">
              <p className="text-sm text-gray-600">Branch: {student.branch}</p>
              {student.emergencyContact && (
                <p className="text-sm text-gray-600">Emergency: {student.emergencyContact}</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Academic Statistics */}
        <Card>
          <CardHeader>
            <CardTitle>Academic Overview</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Active Enrollments</span>
              <span className="font-semibold">
                {student.enrollments.filter(e => e.status === 'ACTIVE').length}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Total Classes</span>
              <span className="font-semibold">{totalClasses}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Attendance Rate</span>
              <span className={`font-semibold ${attendanceRate >= 80 ? 'text-green-600' : attendanceRate >= 60 ? 'text-yellow-600' : 'text-red-600'}`}>
                {attendanceRate.toFixed(1)}%
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Current Level</span>
              <Badge>{student.level}</Badge>
            </div>
          </CardContent>
        </Card>

        {/* Payment Statistics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <CreditCard className="h-5 w-5 mr-2" />
              Payment Overview
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Total Payments</span>
              <span className="font-semibold">{formatCurrency(totalPayments)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Paid Amount</span>
              <span className="font-semibold text-green-600">{formatCurrency(paidPayments)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Pending Amount</span>
              <span className="font-semibold text-red-600">{formatCurrency(pendingPayments)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Payment Records</span>
              <span className="font-semibold">{student.payments.length}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Enrollments */}
      <Card>
        <CardHeader>
          <CardTitle>Course Enrollments</CardTitle>
          <CardDescription>Current and past course enrollments</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Course</TableHead>
                <TableHead>Group</TableHead>
                <TableHead>Teacher</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Start Date</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {student.enrollments.map((enrollment) => (
                <TableRow key={enrollment.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{enrollment.group.course.name}</div>
                      <div className="text-sm text-gray-500">Level: {enrollment.group.course.level}</div>
                    </div>
                  </TableCell>
                  <TableCell>{enrollment.group.name}</TableCell>
                  <TableCell>{enrollment.group.teacher.user.name}</TableCell>
                  <TableCell>{enrollment.group.course.duration} weeks</TableCell>
                  <TableCell>{formatDate(enrollment.startDate)}</TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(enrollment.status)}>
                      {enrollment.status}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Recent Attendance */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Attendance</CardTitle>
          <CardDescription>Latest attendance records</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Group</TableHead>
                <TableHead>Topic</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Notes</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {student.attendances.slice(0, 10).map((attendance) => (
                <TableRow key={attendance.id}>
                  <TableCell>{formatDate(attendance.class.date)}</TableCell>
                  <TableCell>{attendance.class.group.name}</TableCell>
                  <TableCell>{attendance.class.topic || 'No topic'}</TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(attendance.status)}>
                      <div className="flex items-center">
                        {getAttendanceIcon(attendance.status)}
                        <span className="ml-1">{attendance.status}</span>
                      </div>
                    </Badge>
                  </TableCell>
                  <TableCell>{attendance.notes || '-'}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Recent Payments */}
      <Card>
        <CardHeader>
          <CardTitle>Payment History</CardTitle>
          <CardDescription>Recent payment transactions</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Method</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {student.payments.slice(0, 10).map((payment) => (
                <TableRow key={payment.id}>
                  <TableCell>{formatDate(payment.createdAt)}</TableCell>
                  <TableCell className="font-medium">{formatCurrency(payment.amount)}</TableCell>
                  <TableCell>{payment.method}</TableCell>
                  <TableCell>{payment.description || 'Course payment'}</TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(payment.status)}>
                      {payment.status}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}

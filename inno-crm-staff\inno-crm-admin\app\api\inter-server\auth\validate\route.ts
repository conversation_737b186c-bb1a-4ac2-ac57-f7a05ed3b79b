// Inter-Server Authentication Validation Endpoint
// Allows staff server to validate users against admin server

import { NextRequest, NextResponse } from 'next/server';
import { validateInterServerAuth, InterServerUtils } from '@/lib/inter-server';
import bcrypt from 'bcryptjs';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    // Validate inter-server authentication
    if (!validateInterServerAuth(request)) {
      InterServerUtils.logRequest('incoming', '/api/inter-server/auth/validate', false, 'Unauthorized');
      return NextResponse.json(
        { error: 'Unauthorized inter-server request' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { phone, password } = body;

    if (!phone || !password) {
      return NextResponse.json(
        { error: 'Phone and password are required' },
        { status: 400 }
      );
    }

    // Find user in database
    const user = await prisma.user.findUnique({
      where: { phone },
      select: {
        id: true,
        name: true,
        phone: true,
        email: true,
        role: true,
        password: true,
        isActive: true,
        branchId: true,
        branch: {
          select: {
            id: true,
            name: true,
          }
        }
      }
    });

    if (!user) {
      InterServerUtils.logRequest('incoming', '/api/inter-server/auth/validate', false, 'User not found');
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      );
    }

    if (!user.isActive) {
      InterServerUtils.logRequest('incoming', '/api/inter-server/auth/validate', false, 'User inactive');
      return NextResponse.json(
        { error: 'User account is inactive' },
        { status: 401 }
      );
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password);

    if (!isValidPassword) {
      InterServerUtils.logRequest('incoming', '/api/inter-server/auth/validate', false, 'Invalid password');
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      );
    }

    // Check if user role is allowed on requesting server
    const requestingServer = request.headers.get('User-Agent')?.includes('staff') ? 'staff' : 'admin';
    const staffRoles = ['RECEPTION', 'ACADEMIC_MANAGER', 'TEACHER', 'MANAGER', 'STUDENT'];
    const adminRoles = ['ADMIN', 'CASHIER'];

    if (requestingServer === 'staff' && !staffRoles.includes(user.role)) {
      InterServerUtils.logRequest('incoming', '/api/inter-server/auth/validate', false, 'Role not allowed on staff server');
      return NextResponse.json(
        { error: 'Access denied for this server' },
        { status: 403 }
      );
    }

    // Return user data (excluding password)
    const { password: _, ...userWithoutPassword } = user;
    
    InterServerUtils.logRequest('incoming', '/api/inter-server/auth/validate', true, `User ${user.id} validated`);

    return NextResponse.json({
      success: true,
      user: userWithoutPassword,
    });

  } catch (error) {
    console.error('Inter-server auth validation error:', error);
    InterServerUtils.logRequest('incoming', '/api/inter-server/auth/validate', false, error);
    
    return NextResponse.json(
      { 
        error: 'Authentication validation failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

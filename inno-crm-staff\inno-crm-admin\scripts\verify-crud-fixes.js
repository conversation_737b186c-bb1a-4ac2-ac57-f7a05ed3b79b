const fs = require('fs')
const path = require('path')

console.log('🔍 Verifying CRUD Fixes Implementation')
console.log('=' .repeat(50))

// Files that should exist after our fixes
const requiredFiles = [
  // Fixed attendance API
  'app/api/attendance/[id]/route.ts',
  
  // Fixed users API  
  'app/api/users/[id]/route.ts',
  
  // Enhanced activity-logs API
  'app/api/activity-logs/route.ts',
  
  // New classes API
  'app/api/classes/route.ts',
  'app/api/classes/[id]/route.ts',
  
  // Documentation
  'CRUD_FIXES_IMPLEMENTATION_SUMMARY.md',
  'scripts/analyze-crud-operations.js',
  'scripts/test-crud-operations.js'
]

// Files that should NOT exist (removed)
const removedFiles = [
  'app/api/test-templates',
  'app/(dashboard)/dashboard/admin/test-templates'
]

console.log('\n📁 Checking required files...')
let allFilesExist = true

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`)
  } else {
    console.log(`❌ ${file} - MISSING`)
    allFilesExist = false
  }
})

console.log('\n🗑️  Checking removed files...')
let allFilesRemoved = true

removedFiles.forEach(file => {
  if (!fs.existsSync(file)) {
    console.log(`✅ ${file} - REMOVED`)
  } else {
    console.log(`❌ ${file} - STILL EXISTS`)
    allFilesRemoved = false
  }
})

console.log('\n🔍 Checking API route implementations...')

// Check specific implementations
const apiChecks = [
  {
    file: 'app/api/attendance/[id]/route.ts',
    methods: ['GET', 'PUT', 'DELETE'],
    description: 'Attendance individual operations'
  },
  {
    file: 'app/api/users/[id]/route.ts', 
    methods: ['GET', 'PUT', 'DELETE'],
    description: 'Users individual operations'
  },
  {
    file: 'app/api/activity-logs/route.ts',
    methods: ['GET', 'POST'],
    description: 'Activity logs with POST method'
  },
  {
    file: 'app/api/classes/route.ts',
    methods: ['GET', 'POST'],
    description: 'Classes main operations'
  },
  {
    file: 'app/api/classes/[id]/route.ts',
    methods: ['GET', 'PUT', 'DELETE'],
    description: 'Classes individual operations'
  }
]

let allImplementationsCorrect = true

apiChecks.forEach(check => {
  console.log(`\n📄 ${check.description}:`)
  
  if (fs.existsSync(check.file)) {
    const content = fs.readFileSync(check.file, 'utf8')
    
    check.methods.forEach(method => {
      const pattern = new RegExp(`export\\s+async\\s+function\\s+${method}`)
      if (pattern.test(content)) {
        console.log(`  ✅ ${method} method implemented`)
      } else {
        console.log(`  ❌ ${method} method missing`)
        allImplementationsCorrect = false
      }
    })
    
    // Check for proper imports
    const hasAuth = /getServerSession|authOptions/.test(content)
    const hasPrisma = /prisma/.test(content)
    const hasValidation = /zod|z\./.test(content)
    const hasLogging = /ActivityLogger/.test(content)
    
    console.log(`  📦 Dependencies:`)
    console.log(`    ${hasAuth ? '✅' : '❌'} Authentication`)
    console.log(`    ${hasPrisma ? '✅' : '❌'} Database (Prisma)`)
    console.log(`    ${hasValidation ? '✅' : '❌'} Validation (Zod)`)
    console.log(`    ${hasLogging ? '✅' : '❌'} Activity Logging`)
    
  } else {
    console.log(`  ❌ File does not exist`)
    allImplementationsCorrect = false
  }
})

console.log('\n' + '='.repeat(50))
console.log('📊 VERIFICATION SUMMARY')
console.log('='.repeat(50))

console.log(`\n📁 Files Status:`)
console.log(`  ✅ Required files: ${allFilesExist ? 'All present' : 'Some missing'}`)
console.log(`  🗑️  Removed files: ${allFilesRemoved ? 'All removed' : 'Some still exist'}`)
console.log(`  🔧 Implementations: ${allImplementationsCorrect ? 'All correct' : 'Some issues'}`)

const overallSuccess = allFilesExist && allFilesRemoved && allImplementationsCorrect

console.log(`\n🎯 Overall Status: ${overallSuccess ? '✅ SUCCESS' : '❌ ISSUES FOUND'}`)

if (overallSuccess) {
  console.log('\n🎉 All CRUD fixes have been successfully implemented!')
  console.log('\n📋 Next Steps:')
  console.log('  1. Run: npm run dev (if not already running)')
  console.log('  2. Test: node scripts/analyze-crud-operations.js')
  console.log('  3. Optional: node scripts/test-crud-operations.js (requires auth)')
  console.log('  4. Verify frontend integration manually')
} else {
  console.log('\n⚠️  Please review and fix the issues above before proceeding.')
}

console.log('\n📄 For detailed information, see: CRUD_FIXES_IMPLEMENTATION_SUMMARY.md')

// Generate a quick status report
const statusReport = {
  timestamp: new Date().toISOString(),
  filesStatus: {
    requiredFiles: allFilesExist,
    removedFiles: allFilesRemoved
  },
  implementationStatus: allImplementationsCorrect,
  overallSuccess,
  checkedFiles: requiredFiles.length,
  apiEndpointsVerified: apiChecks.length
}

fs.writeFileSync('crud-fixes-verification.json', JSON.stringify(statusReport, null, 2))
console.log('\n📄 Verification report saved to: crud-fixes-verification.json')

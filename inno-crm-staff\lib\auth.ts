import { NextAuthOptions } from "next-auth"
import Credential<PERSON><PERSON>rovider from "next-auth/providers/credentials"

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        phone: { label: "Phone", type: "text" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.phone || !credentials?.password) {
          return null
        }

        try {
          // Call the admin server to verify user credentials
          const adminServerUrl = process.env.ADMIN_SERVER_URL || 'http://localhost:3000'
          const response = await fetch(`${adminServerUrl}/api/auth/verify`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              phone: credentials.phone,
              password: credentials.password,
              serverKey: process.env.INTER_SERVER_SECRET,
            }),
          })

          if (!response.ok) {
            console.error('Admin server auth failed:', response.status, response.statusText)
            return null
          }

          const data = await response.json()

          if (!data.success || !data.user) {
            return null
          }

          const user = data.user

          // Verify the user role is allowed on staff server
          const allowedRoles = ['MANAGER', 'TEACHER', 'RECEPTION', 'STUDENT', 'ACADEMIC_MANAGER']
          if (!allowedRoles.includes(user.role)) {
            console.error('User role not allowed on staff server:', user.role)
            return null
          }

          return {
            id: user.id,
            phone: user.phone,
            name: user.name,
            email: user.email,
            role: user.role,
          }
        } catch (error) {
          console.error('Error verifying user with admin server:', error)
          return null
        }
      }
    })
  ],
  session: {
    strategy: "jwt"
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role || null
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!
        session.user.role = (token.role as string) || null
      }
      return session
    }
  },
  pages: {
    signIn: "/auth/signin",
  }
}

'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  getActivityDescription, 
  getActivityIcon, 
  getActivityPriority, 
  getTimeAgo,
  type ActivityLogEntry 
} from '@/lib/activity-utils'
import { formatDate } from '@/lib/utils'
import { Activity, RefreshCw, ExternalLink } from 'lucide-react'
import Link from 'next/link'

interface ActivityFeedProps {
  limit?: number
  showHeader?: boolean
  showRefresh?: boolean
  showViewAll?: boolean
  userId?: string // Filter by specific user
  resource?: string // Filter by specific resource
  className?: string
}

export function ActivityFeed({ 
  limit = 10, 
  showHeader = true, 
  showRefresh = true,
  showViewAll = true,
  userId,
  resource,
  className 
}: ActivityFeedProps) {
  const [activities, setActivities] = useState<ActivityLogEntry[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchActivities = useCallback(async () => {
    setLoading(true)
    setError(null)
    
    try {
      const params = new URLSearchParams({
        limit: limit.toString(),
        page: '1',
      })
      
      if (userId) params.append('userId', userId)
      if (resource) params.append('resource', resource)

      const response = await fetch(`/api/activity-logs?${params}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch activities')
      }

      const data = await response.json()
      setActivities(data.logs || [])
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load activities')
    } finally {
      setLoading(false)
    }
  }, [limit, userId, resource])

  useEffect(() => {
    fetchActivities()
  }, [fetchActivities])

  const getPriorityColor = (priority: 'low' | 'medium' | 'high') => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low': return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'ADMIN': return 'bg-red-100 text-red-800'
      case 'MANAGER': return 'bg-blue-100 text-blue-800'
      case 'TEACHER': return 'bg-green-100 text-green-800'
      case 'RECEPTION': return 'bg-yellow-100 text-yellow-800'
      case 'CASHIER': return 'bg-purple-100 text-purple-800'
      case 'STUDENT': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <Activity className="h-8 w-8 mx-auto mb-2" />
            <p>Failed to load activity feed</p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={fetchActivities}
              className="mt-2"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      {showHeader && (
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <Activity className="h-5 w-5 mr-2" />
                Recent Activity
              </CardTitle>
              <CardDescription>
                Latest system activities and user actions
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              {showRefresh && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={fetchActivities}
                  disabled={loading}
                >
                  <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                </Button>
              )}
              {showViewAll && (
                <Button variant="outline" size="sm" asChild>
                  <Link href="/dashboard/admin/activity-logs">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    View All
                  </Link>
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      )}
      
      <CardContent className="p-0">
        {loading ? (
          <div className="p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-sm text-gray-500 mt-2">Loading activities...</p>
          </div>
        ) : activities.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No recent activities</p>
          </div>
        ) : (
          <ScrollArea className="h-96">
            <div className="p-4 space-y-3">
              {activities.map((activity, index) => {
                const priority = getActivityPriority(activity)
                const description = getActivityDescription(activity)
                const icon = getActivityIcon(activity.action)
                const timeAgo = getTimeAgo(activity.createdAt)
                
                return (
                  <div
                    key={activity.id}
                    className={`flex items-start space-x-3 p-3 rounded-lg border transition-colors hover:bg-gray-50 ${
                      index === 0 ? 'bg-blue-50 border-blue-200' : 'border-gray-200'
                    }`}
                  >
                    {/* Activity Icon */}
                    <div className="flex-shrink-0">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm ${
                        priority === 'high' ? 'bg-red-100' : 
                        priority === 'medium' ? 'bg-yellow-100' : 'bg-gray-100'
                      }`}>
                        {icon}
                      </div>
                    </div>
                    
                    {/* Activity Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {description}
                        </p>
                        <div className="flex items-center space-x-2">
                          <Badge className={getRoleColor(activity.userRole)} variant="secondary">
                            {activity.userRole}
                          </Badge>
                          {priority === 'high' && (
                            <Badge variant="destructive" className="text-xs">
                              High
                            </Badge>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <p className="text-xs text-gray-500">
                          {activity.user.name}
                        </p>
                        <p className="text-xs text-gray-500" title={formatDate(activity.createdAt)}>
                          {timeAgo}
                        </p>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  )
}
